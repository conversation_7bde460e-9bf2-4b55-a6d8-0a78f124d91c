import { useState, useCallback, useMemo } from 'react';

/**
 * useDhikrCounter Hook
 * Islamic dhikr counter with traditional counts (33, 99, etc.)
 */
export function useDhikrCounter(initialTarget: number = 33) {
  const [count, setCount] = useState(0);
  const [target, setTarget] = useState(initialTarget);

  const increment = useCallback(() => {
    setCount(prev => prev + 1);
  }, []);

  const reset = useCallback(() => {
    setCount(0);
  }, []);

  const updateTarget = useCallback((newTarget: number) => {
    setTarget(newTarget);
  }, []);

  const progress = useMemo(() => {
    return target > 0 ? (count / target) * 100 : 0;
  }, [count, target]);

  const isCompleted = useMemo(() => {
    return count >= target;
  }, [count, target]);

  const remaining = useMemo(() => {
    return Math.max(0, target - count);
  }, [count, target]);

  // Traditional Islamic dhikr counts
  const traditionalCounts = [33, 99, 100, 1000];

  return {
    count,
    target,
    progress,
    isCompleted,
    remaining,
    increment,
    reset,
    updateTarget,
    traditionalCounts,
  };
}

export default useDhikrCounter;
