import { useState, useCallback, useMemo } from 'react';

/**
 * useJourneyProgress Hook
 * Track progress through healing journeys
 */
export function useJourneyProgress(totalDays: number) {
  const [completedDays, setCompletedDays] = useState(0);
  const [currentDay, setCurrentDay] = useState(1);
  const [dailyPractices, setDailyPractices] = useState<Record<number, boolean>>({});

  const completeDay = useCallback((day: number) => {
    setDailyPractices(prev => {
      const updated = { ...prev, [day]: true };
      const newCompletedCount = Object.values(updated).filter(Boolean).length;
      setCompletedDays(newCompletedCount);
      
      // Advance to next day if completing current day
      if (day === currentDay && day < totalDays) {
        setCurrentDay(day + 1);
      }
      
      return updated;
    });
  }, [currentDay, totalDays]);

  const resetJourney = useCallback(() => {
    setCompletedDays(0);
    setCurrentDay(1);
    setDailyPractices({});
  }, []);

  const isDayCompleted = useCallback((day: number) => {
    return dailyPractices[day] || false;
  }, [dailyPractices]);

  const progress = useMemo(() => {
    return totalDays > 0 ? (completedDays / totalDays) * 100 : 0;
  }, [completedDays, totalDays]);

  const isCompleted = useMemo(() => {
    return completedDays === totalDays;
  }, [completedDays, totalDays]);

  const daysRemaining = useMemo(() => {
    return Math.max(0, totalDays - completedDays);
  }, [totalDays, completedDays]);

  return {
    completedDays,
    currentDay,
    progress,
    isCompleted,
    daysRemaining,
    completeDay,
    resetJourney,
    isDayCompleted,
  };
}

export default useJourneyProgress;
