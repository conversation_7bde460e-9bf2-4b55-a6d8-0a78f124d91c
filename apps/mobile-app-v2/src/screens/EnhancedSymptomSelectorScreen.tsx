import { AntD<PERSON>, Feather } from "@expo/vector-icons";
import { RouteProp, useNavigation, useRoute } from "@react-navigation/native";
import { StackNavigationProp } from "@react-navigation/stack";
import * as Haptics from "expo-haptics";
import { LinearGradient } from "expo-linear-gradient";
import React, { useEffect, useLayoutEffect, useState } from "react";
import {
  ActivityIndicator,
  Alert,
  FlatList,
  KeyboardAvoidingView,
  Platform,
  SafeAreaView,
  ScrollView,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from "react-native";

import { CategoryTab } from "../components/CategoryTab";
import SymptomSelectorContent from "../components/symptoms/SymptomSelectorContent";
import { Card } from "../components/ui/Card";
import { colors } from "../constants/Colors";
import { SymptomSubmission } from "../services/api/types";
import serviceRegistry from "../services/ServiceRegistry";

// Define navigation params for type safety
type RootStackParamList = {
  Home: undefined;
  SymptomSelector: {
    initialSymptoms?: Partial<SymptomSubmission>;
    onSubmitRedirect?: string;
  };
  JourneyStartScreen: {
    diagnosis: any;
    symptoms: SymptomSubmission;
  };
  Dashboard: undefined;
};

type SymptomSelectorScreenNavigationProp = StackNavigationProp<
  RootStackParamList,
  "SymptomSelector"
>;

type SymptomSelectorScreenRouteProp = RouteProp<
  RootStackParamList,
  "SymptomSelector"
>;

// Define symptom type
type Symptom = {
  id: string;
  name: string;
  category: "jism" | "nafs" | "aql" | "qalb" | "ruh";
  description?: string;
};

// Define category type
type CategoryType = "jism" | "nafs" | "aql" | "qalb" | "ruh" | "all";

export default function EnhancedSymptomSelectorScreen() {
  const navigation = useNavigation<SymptomSelectorScreenNavigationProp>();
  const route = useRoute<SymptomSelectorScreenRouteProp>();

  // Symptoms context
  const { state: symptomsState, fetchSymptoms } = useSymptoms();

  // Get initial symptoms from route params if they exist
  const initialSymptoms = route.params?.initialSymptoms;
  const onSubmitRedirect =
    route.params?.onSubmitRedirect || "JourneyStartScreen";

  // State for loading and error handling
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);

  // State for search functionality
  const [searchText, setSearchText] = useState("");
  const [suggestedSymptoms, setSuggestedSymptoms] = useState<Symptom[]>([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<CategoryType>("all");
  const [allSymptoms, setAllSymptoms] = useState<Symptom[]>([]);
  const [filteredSymptoms, setFilteredSymptoms] = useState<Symptom[]>([]);

  // Configure the header
  useLayoutEffect(() => {
    navigation.setOptions({
      headerTitle: "Symptom Analysis",
      headerTitleAlign: "center",
      headerStyle: {
        backgroundColor: Colors.primary,
      },
      headerTintColor: "#fff",
      headerTitleStyle: {
        fontWeight: "bold",
      },
      headerLeft: () => (
        <AntDesign
          name="arrowleft"
          size={24}
          color="#fff"
          style={{ marginLeft: 16 }}
          onPress={() => {
            // Show confirmation if user has selected symptoms
            Alert.alert(
              "Leave Symptom Analysis?",
              "Your progress will be lost. Are you sure you want to go back?",
              [
                { text: "Stay", style: "cancel" },
                { text: "Leave", onPress: () => navigation.goBack() },
              ]
            );
          }}
        />
      ),
    });
  }, [navigation]);

  // Load symptoms on mount
  useEffect(() => {
    if (symptomsState.categories.length === 0) {
      fetchSymptoms();
    } else {
      // Convert categories to Symptom[] format
      const symptoms: Symptom[] = [];

      symptomsState.categories.forEach((category) => {
        category.symptoms.forEach((item) => {
          symptoms.push({
            id: `${category.id}-${item}`,
            name: item,
            category: category.id as "jism" | "nafs" | "aql" | "qalb" | "ruh",
          });
        });
      });

      setAllSymptoms(symptoms);
      setFilteredSymptoms(symptoms);
    }
  }, [symptomsState.categories]);

  // Update loading state based on symptoms context
  useEffect(() => {
    setLoading(symptomsState.loading);
    setError(symptomsState.error);
  }, [symptomsState.loading, symptomsState.error]);

  // Handle search text change
  const handleSearchChange = (text: string) => {
    setSearchText(text);

    if (text.length > 1) {
      setShowSuggestions(true);

      // Filter symptoms based on search text
      const results = allSymptoms.filter((symptom) =>
        symptom.name.toLowerCase().includes(text.toLowerCase())
      );

      setSuggestedSymptoms(results.slice(0, 5)); // Limit to 5 suggestions
    } else {
      setShowSuggestions(false);
    }
  };

  // Handle category selection
  const handleCategorySelect = (category: CategoryType) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    setSelectedCategory(category);

    if (category === "all") {
      setFilteredSymptoms(allSymptoms);
    } else {
      setFilteredSymptoms(
        allSymptoms.filter((symptom) => symptom.category === category)
      );
    }
  };

  // Handle submission of symptoms
  const handleSubmit = (symptoms: SymptomSubmission, diagnosis: any) => {
    try {
      setIsAnalyzing(true);
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);

      // Navigate to the appropriate screen based on onSubmitRedirect
      if (onSubmitRedirect === "JourneyStartScreen") {
        navigation.replace("JourneyStartScreen", {
          symptoms,
          diagnosis,
        });
      } else {
        // Default to dashboard if redirect is not specified
        navigation.replace("Dashboard");
      }
    } catch (err) {
      console.error("Error handling symptom submission:", err);
      setError("Failed to process your symptoms. Please try again.");
      setIsAnalyzing(false);
    }
  };

  // Handle cancellation
  const handleCancel = () => {
    Alert.alert(
      "Cancel Symptom Analysis?",
      "Your progress will be lost. Are you sure you want to cancel?",
      [
        { text: "No", style: "cancel" },
        { text: "Yes", onPress: () => navigation.goBack() },
      ]
    );
  };

  // Render suggestion item
  const renderSuggestionItem = ({ item }: { item: Symptom }) => (
    <TouchableOpacity
      style={styles.suggestionItem}
      onPress={() => {
        setSearchText("");
        setShowSuggestions(false);
        // Set category to the symptom's category
        handleCategorySelect(item.category);
      }}
    >
      <Text style={styles.suggestionText}>{item.name}</Text>
      <Text style={styles.categoryLabel}>
        {item.category.charAt(0).toUpperCase() + item.category.slice(1)}
      </Text>
    </TouchableOpacity>
  );

  // Display loading indicator if loading
  if (loading) {
    return (
      <View style={styles.centerContainer}>
        <ActivityIndicator size="large" color={Colors.primary} />
        <Text style={styles.loadingText}>Loading symptom categories...</Text>
      </View>
    );
  }

  // Display error message if there's an error
  if (error) {
    return (
      <View style={styles.centerContainer}>
        <AntDesign name="exclamationcircle" size={48} color={Colors.error} />
        <Text style={styles.errorText}>{error}</Text>
        <TouchableOpacity
          style={styles.retryButton}
          onPress={() => {
            setLoading(true);
            // Re-fetch symptoms
            serviceRegistry
              .getSymptoms()
              .then((service) => service.getSymptomCategories())
              .then((categories) => {
                // Convert to Symptom[] format as done in useEffect
                const symptoms: Symptom[] = [];

                Object.entries(categories).forEach(([category, items]) => {
                  items.forEach((item) => {
                    symptoms.push({
                      id: `${category}-${item}`,
                      name: item,
                      category: category as
                        | "jism"
                        | "nafs"
                        | "aql"
                        | "qalb"
                        | "ruh",
                    });
                  });
                });

                setAllSymptoms(symptoms);
                setFilteredSymptoms(symptoms);
                setError(null);
              })
              .catch((err) => {
                console.error("Retry failed:", err);
                setError(
                  "Failed to load symptom categories. Please try again."
                );
              })
              .finally(() => setLoading(false));
          }}
        >
          <Text style={styles.retryButtonText}>Retry</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        style={styles.keyboardAvoidingView}
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        keyboardVerticalOffset={Platform.OS === "ios" ? 90 : 0}
      >
        {/* Search Bar */}
        <View style={styles.searchContainer}>
          <View style={styles.searchInputContainer}>
            <Feather
              name="search"
              size={20}
              color={Colors.textSecondary}
              style={styles.searchIcon}
            />
            <TextInput
              style={styles.searchInput}
              placeholder="Search symptoms..."
              placeholderTextColor={Colors.textSecondary}
              value={searchText}
              onChangeText={handleSearchChange}
            />
            {searchText.length > 0 && (
              <TouchableOpacity
                style={styles.clearButton}
                onPress={() => {
                  setSearchText("");
                  setShowSuggestions(false);
                }}
              >
                <Feather name="x" size={20} color={Colors.textSecondary} />
              </TouchableOpacity>
            )}
          </View>

          {showSuggestions && suggestedSymptoms.length > 0 && (
            <Card style={styles.suggestionsCard}>
              <FlatList
                data={suggestedSymptoms}
                renderItem={renderSuggestionItem}
                keyExtractor={(item) => item.id}
                style={styles.suggestionsList}
              />
            </Card>
          )}
        </View>

        {/* Category Tabs */}
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          style={styles.categoriesContainer}
          contentContainerStyle={styles.categoriesContent}
        >
          <TouchableOpacity
            style={[
              styles.allCategoryTab,
              selectedCategory === "all" && styles.activeCategoryTab,
            ]}
            onPress={() => handleCategorySelect("all")}
          >
            <Text
              style={[
                styles.categoryTabText,
                selectedCategory === "all" && styles.activeCategoryTabText,
              ]}
            >
              All
            </Text>
          </TouchableOpacity>

          <CategoryTab
            category="jism"
            active={selectedCategory === "jism"}
            onPress={() => handleCategorySelect("jism")}
          />

          <CategoryTab
            category="nafs"
            active={selectedCategory === "nafs"}
            onPress={() => handleCategorySelect("nafs")}
          />

          <CategoryTab
            category="aql"
            active={selectedCategory === "aql"}
            onPress={() => handleCategorySelect("aql")}
          />

          <CategoryTab
            category="qalb"
            active={selectedCategory === "qalb"}
            onPress={() => handleCategorySelect("qalb")}
          />

          <CategoryTab
            category="ruh"
            active={selectedCategory === "ruh"}
            onPress={() => handleCategorySelect("ruh")}
          />
        </ScrollView>

        {/* Main Symptom Selector Content */}
        <SymptomSelectorContent
          onSubmit={handleSubmit}
          onCancel={handleCancel}
          initialSymptoms={initialSymptoms}
        />

        {/* Footer with Gradient */}
        <View style={styles.footer}>
          <LinearGradient
            colors={["rgba(255,255,255,0)", Colors.background]}
            style={styles.footerGradient}
          />
          <TouchableOpacity
            style={[
              styles.analyzeButton,
              isAnalyzing && styles.analyzeButtonDisabled,
            ]}
            onPress={() => {
              // This will be handled by SymptomSelectorContent
            }}
            disabled={isAnalyzing}
          >
            {isAnalyzing ? (
              <ActivityIndicator size="small" color="white" />
            ) : (
              <>
                <Feather
                  name="activity"
                  size={20}
                  color="white"
                  style={styles.buttonIcon}
                />
                <Text style={styles.analyzeButtonText}>
                  Analyze My Symptoms
                </Text>
              </>
            )}
          </TouchableOpacity>
        </View>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#F9F9F9",
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  centerContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 16,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: Colors.textSecondary,
    textAlign: "center",
  },
  errorText: {
    marginTop: 16,
    fontSize: 16,
    color: Colors.error,
    textAlign: "center",
    marginHorizontal: 32,
  },
  retryButton: {
    marginTop: 16,
    paddingVertical: 10,
    paddingHorizontal: 20,
    backgroundColor: Colors.primary,
    borderRadius: 8,
  },
  retryButtonText: {
    fontSize: 16,
    color: "white",
    fontWeight: "600",
  },
  searchContainer: {
    paddingHorizontal: 16,
    marginBottom: 12,
    zIndex: 10,
  },
  searchInputContainer: {
    flexDirection: "row",
    alignItems: "center",
    borderWidth: 1,
    borderColor: Colors.border,
    borderRadius: 8,
    paddingHorizontal: 12,
    backgroundColor: "white",
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    height: 40,
    fontSize: 16,
    color: Colors.text,
  },
  clearButton: {
    padding: 4,
  },
  suggestionsCard: {
    position: "absolute",
    top: "100%",
    left: 16,
    right: 16,
    marginTop: 4,
    zIndex: 20,
    elevation: 5,
  },
  suggestionsList: {
    maxHeight: 200,
  },
  suggestionItem: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: "rgba(0, 0, 0, 0.05)",
  },
  suggestionText: {
    fontSize: 16,
    color: Colors.text,
  },
  categoryLabel: {
    fontSize: 12,
    color: Colors.textSecondary,
    marginLeft: 8,
  },
  categoriesContainer: {
    marginBottom: 12,
    zIndex: 5,
  },
  categoriesContent: {
    paddingHorizontal: 16,
  },
  allCategoryTab: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: Colors.border,
    marginRight: 8,
    backgroundColor: "white",
  },
  activeCategoryTab: {
    backgroundColor: Colors.primary + "20",
    borderColor: Colors.primary,
  },
  categoryTabText: {
    fontSize: 14,
    fontWeight: "600",
    color: Colors.text,
  },
  activeCategoryTabText: {
    color: Colors.primary,
  },
  footer: {
    padding: 16,
    paddingBottom: Platform.OS === "ios" ? 24 : 16,
    position: "relative",
  },
  footerGradient: {
    position: "absolute",
    bottom: 0,
    left: 0,
    right: 0,
    height: 40,
  },
  analyzeButton: {
    backgroundColor: Colors.primary,
    borderRadius: 8,
    paddingVertical: 14,
    alignItems: "center",
    justifyContent: "center",
    flexDirection: "row",
  },
  analyzeButtonDisabled: {
    backgroundColor: Colors.primaryLight,
    opacity: 0.7,
  },
  analyzeButtonText: {
    color: "white",
    fontSize: 16,
    fontWeight: "bold",
  },
  buttonIcon: {
    marginRight: 8,
  },
});
