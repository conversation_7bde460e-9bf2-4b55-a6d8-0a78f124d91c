import React, { useEffect, useState, useCallback } from 'react';

import {
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  FlatList,
  ActivityIndicator,
  RefreshControl,
  SectionList,
} from 'react-native';

import { Feather } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import * as Haptics from 'expo-haptics';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

import {
  Journey,
  JourneyProgress,
  JourneyCategory,
  getAllJourneys,
  getUserJourneyProgress,
} from '../api/journeys';
import { JourneyCard } from '../components/JourneyCard';
import { Badge } from '../components/ui/Badge';
import { Card } from '../components/ui/Card';
import { Text } from '../components/ui/Text';
import { View } from '../components/ui/View';
import { colors } from '../constants/Colors';
import Theme from '../constants/Theme';
import // useColorScheme from '../hooks/useColorScheme';
import { RootStackNavigationProp } from '../navigation/types';

// Define section data interface
interface JourneySection {
  title: string;
  data: Journey[];
}

export default function JourneysScreen() {
  const navigation = useNavigation<RootStackNavigationProp>();
  
  
  const insets = useSafeAreaInsets();

  // State
  const [journeys, setJourneys] = useState<Journey[]>([]);
  const [userProgress, setUserProgress] = useState<JourneyProgress[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<JourneyCategory | 'all'>('all');
  const [activeJourneys, setActiveJourneys] = useState<Journey[]>([]);
  const [sections, setSections] = useState<JourneySection[]>([]);

  // Load journeys and user progress
  const loadData = useCallback(async () => {
    try {
      const [journeysData, progressData] = await Promise.all([
        getAllJourneys(),
        getUserJourneyProgress(),
      ]);

      setJourneys(journeysData);
      setUserProgress(progressData);

      // Set active journeys (in progress)
      const activeJourneyIds = progressData
        .filter(p => p.status === 'in_progress')
        .map(p => p.journeyId);

      const active = journeysData.filter(j => activeJourneyIds.includes(j.id));
      setActiveJourneys(active);

      // Group journeys by duration
      organizeJourneysByDuration(journeysData);
    } catch (error) {
      console.error('Error loading journeys data:', error);
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  }, []);

  // Group journeys by duration
  const organizeJourneysByDuration = useCallback((journeyData: Journey[]) => {
    // Filter journeys based on selected category
    const filteredJourneys = selectedCategory === 'all'
      ? journeyData
      : journeyData.filter(j => j.category === selectedCategory);

    // Create sections
    const newSections: JourneySection[] = [
      {
        title: '7-Day Mini-Toolkits',
        data: filteredJourneys.filter(j => j.duration === 7),
      },
      {
        title: '14-Day Journeys',
        data: filteredJourneys.filter(j => j.duration === 14),
      },
      {
        title: '21-Day Journeys',
        data: filteredJourneys.filter(j => j.duration === 21),
      },
      {
        title: '40-Day Transformations',
        data: filteredJourneys.filter(j => j.duration === 40),
      },
    ];

    // Remove empty sections
    const filteredSections = newSections.filter(section => section.data.length > 0);
    setSections(filteredSections);
  }, [selectedCategory]);

  // Load data on mount
  useEffect(() => {
    loadData();
  }, [loadData]);

  // Update sections when category changes
  useEffect(() => {
    if (journeys.length > 0) {
      organizeJourneysByDuration(journeys);
    }
  }, [selectedCategory, journeys, organizeJourneysByDuration]);

  // Handle refresh
  const handleRefresh = useCallback(() => {
    setIsRefreshing(true);
    loadData();
  }, [loadData]);

  // Handle category selection
  const handleCategorySelect = (category: JourneyCategory | 'all') => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    setSelectedCategory(category);
  };

  // Navigate to journey detail
  const navigateToJourneyDetail = (journey: Journey) => {
    navigation.navigate('JourneyDetail', { journeyId: journey.id });
  };

  // Get progress for a journey
  const getJourneyProgress = (journeyId: string) => {
    return userProgress.find(p => p.journeyId === journeyId);
  };

  // Render category pill
  const renderCategoryPill = (category: JourneyCategory | 'all', label: string, icon: string) => {
    const isActive = selectedCategory === category;
    const categoryColor = getCategoryColor(category);

    return (
      <TouchableOpacity
        style={[
          styles.categoryPill,
          {
            backgroundColor: isActive ? categoryColor + '20' : 'transparent',
            borderColor: isActive ? categoryColor : colors.border,
          },
        ]}
        onPress={() => handleCategorySelect(category)}
      >
        <Feather
          name={icon}
          size={16}
          color={isActive ? categoryColor : colors.textSecondary}
          style={styles.categoryIcon}
        />
        <Text
          variant="body"
          style={{
            color: isActive ? categoryColor : colors.text,
          }}
        >
          {label}
        </Text>
      </TouchableOpacity>
    );
  };

  // Get color based on category
  const getCategoryColor = (category: JourneyCategory | 'all') => {
    switch (category) {
      case 'jism':
        return colors.jismRed;
      case 'nafs':
        return colors.nafsOrange;
      case 'aql':
        return colors.aqlYellow;
      case 'qalb':
        return colors.qalbGreen;
      case 'ruh':
        return colors.spiritualBlue;
      case 'all':
      default:
        return colors.primary;
    }
  };

  // Render active journey item
  const renderActiveJourneyItem = ({ item }: { item: Journey }) => {
    const progress = getJourneyProgress(item.id);

    return (
      <JourneyCard
        journey={item}
        progress={progress}
        onPress={navigateToJourneyDetail}
      />
    );
  };

  // Render journey item
  const renderJourneyItem = ({ item }: { item: Journey }) => {
    const progress = getJourneyProgress(item.id);

    return (
      <JourneyCard
        journey={item}
        progress={progress}
        onPress={navigateToJourneyDetail}
      />
    );
  };

  // Render section header
  const renderSectionHeader = ({ section }: { section: JourneySection }) => (
    <View style={styles.sectionHeader}>
      <Text variant="heading3">{section.title}</Text>
    </View>
  );

  // Render loading state
  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={colors.primary} />
        <Text variant="body" style={styles.loadingText}>
          Loading journeys...
        </Text>
      </View>
    );
  }

  return (
    <View style={[styles.container, { paddingTop: insets.top }]}>
      <View style={styles.header}>
        <Text variant="heading2">Healing Journeys</Text>
        <TouchableOpacity
          style={styles.filterButton}
          onPress={() => {}}
        >
          <Feather name="filter" size={20} color={colors.text} />
        </TouchableOpacity>
      </View>

      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        style={styles.categoriesScrollView}
        contentContainerStyle={styles.categoriesContainer}
      >
        {renderCategoryPill('all', 'All', 'grid')}
        {renderCategoryPill('jism', 'Body', 'activity')}
        {renderCategoryPill('nafs', 'Emotions', 'wind')}
        {renderCategoryPill('aql', 'Mind', 'cpu')}
        {renderCategoryPill('qalb', 'Heart', 'heart')}
        {renderCategoryPill('ruh', 'Soul', 'star')}
      </ScrollView>

      {activeJourneys.length > 0 && (
        <View style={styles.activeJourneysSection}>
          <Text variant="heading3" style={styles.sectionTitle}>
            Your Active Journeys
          </Text>
          <FlatList
            data={activeJourneys}
            renderItem={renderActiveJourneyItem}
            keyExtractor={item => item.id}
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.activeJourneysContainer}
          />
        </View>
      )}

      <SectionList
        sections={sections}
        keyExtractor={item => item.id}
        renderItem={renderJourneyItem}
        renderSectionHeader={renderSectionHeader}
        stickySectionHeadersEnabled={false}
        contentContainerStyle={styles.journeysContainer}
        refreshControl={
          <RefreshControl
            refreshing={isRefreshing}
            onRefresh={handleRefresh}
            colors={[colors.primary]}
            tintColor={colors.primary}
          />
        }
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: Theme.spacing.m,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: Theme.spacing.m,
    paddingVertical: Theme.spacing.m,
  },
  filterButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f0f0f0',
  },
  categoriesScrollView: {
    marginBottom: Theme.spacing.m,
  },
  categoriesContainer: {
    paddingHorizontal: Theme.spacing.m,
  },
  categoryPill: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: Theme.spacing.m,
    paddingVertical: Theme.spacing.s,
    borderRadius: Theme.borderRadius.pill,
    borderWidth: 1,
    marginRight: Theme.spacing.s,
  },
  categoryIcon: {
    marginRight: Theme.spacing.xs,
  },
  activeJourneysSection: {
    marginBottom: Theme.spacing.m,
  },
  sectionTitle: {
    paddingHorizontal: Theme.spacing.m,
    marginBottom: Theme.spacing.s,
  },
  activeJourneysContainer: {
    paddingHorizontal: Theme.spacing.m,
  },
  journeysContainer: {
    paddingHorizontal: Theme.spacing.m,
  },
  sectionHeader: {
    marginVertical: Theme.spacing.m,
  },
});

