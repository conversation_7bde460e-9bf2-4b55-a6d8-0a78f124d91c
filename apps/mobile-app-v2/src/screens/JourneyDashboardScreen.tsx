import React, { useEffect, useState } from 'react';
import {
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  Image,
} from 'react-native';
import { Feather } from '@expo/vector-icons';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { LinearGradient } from 'expo-linear-gradient';
import * as Haptics from 'expo-haptics';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

import { useJourneys } from '../state/context/JourneysContext';
import { JourneyActivity } from '../state/types';
import { Badge } from '../components/ui/Badge';
import { Button } from '../components/ui/Button';
import { Card } from '../components/ui/Card';
import { ProgressBar } from '../components/ui/ProgressBar';
import { Text } from '../components/ui/Text';
import { View } from '../components/ui/View';
import { colors } from '../constants/Colors';
import Theme from '../constants/Theme';
import // useColorScheme from '../hooks/useColorScheme';
import { RootStackParamList } from '../navigation/types';

type JourneyDashboardScreenRouteProp = RouteProp<RootStackParamList, 'JourneyDashboard'>;

export default function JourneyDashboardScreen() {
  const route = useRoute<JourneyDashboardScreenRouteProp>();
  const { journeyId } = route.params;
  const navigation = useNavigation();
  const insets = useSafeAreaInsets();
  
  

  const { state, loadActivities, completeActivity } = useJourneys();
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'overview' | 'activities' | 'resources'>('overview');

  // Get the journey from state
  const journey = state.journeys.find(j => j.id === journeyId);
  const userProgress = state.userJourneys[journeyId] || null;
  const activities = state.activities[journeyId] || [];

  // Load activities when the component mounts
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        if (journeyId) {
          await loadActivities(journeyId);
        }
      } catch (error) {
        console.error('Error loading activities:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [journeyId, loadActivities]);

  // Handle activity completion
  const handleCompleteActivity = async (activityId: string) => {
    try {
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
      await completeActivity(journeyId, activityId);
    } catch (error) {
      console.error('Error completing activity:', error);
    }
  };

  // Get the category color based on the journey's category
  const getCategoryColor = () => {
    if (!journey) return colors.primary;

    switch (journey.category) {
      case 'jism':
        return colors.jismRed;
      case 'nafs':
        return colors.nafsOrange;
      case 'aql':
        return colors.aqlYellow;
      case 'qalb':
        return colors.qalbGreen;
      case 'ruh':
        return colors.spiritualBlue;
      case 'all':
      default:
        return colors.primary;
    }
  };

  // Get category name
  const getCategoryName = () => {
    if (!journey) return 'All Layers';

    switch (journey.category) {
      case 'jism':
        return 'Body (Jism)';
      case 'nafs':
        return 'Emotions (Nafs)';
      case 'aql':
        return 'Mind (Aql)';
      case 'qalb':
        return 'Heart (Qalb)';
      case 'ruh':
        return 'Soul (Ruh)';
      case 'all':
      default:
        return 'All Layers';
    }
  };

  // If loading or journey not found
  if (loading || !journey) {
    return (
      <View style={[styles.container, { paddingTop: insets.top }]}>
        <ActivityIndicator size="large" color={colors.primary} />
        <Text variant="body" style={styles.loadingText}>
          {loading ? 'Loading journey...' : 'Journey not found'}
        </Text>
      </View>
    );
  }

  // Calculate progress percentage
  const progressPercentage = userProgress ? userProgress.progress : 0;
  const completedActivities = activities.filter(a => a.completed).length;
  const totalActivities = activities.length;

  // Render the header with journey information
  const renderHeader = () => (
    <View style={styles.header}>
      <TouchableOpacity
        style={styles.backButton}
        onPress={() => navigation.goBack()}
      >
        <Feather name="arrow-left" size={24} color={colors.text} />
      </TouchableOpacity>
      
      <View style={styles.journeyDetails}>
        <View style={styles.titleRow}>
          <Feather
            name={journey.category === 'jism' ? 'activity' : 
                 journey.category === 'nafs' ? 'wind' : 
                 journey.category === 'aql' ? 'cpu' : 
                 journey.category === 'qalb' ? 'heart' : 
                 journey.category === 'ruh' ? 'star' : 'layers'}
            size={20}
            color={getCategoryColor()}
            style={styles.categoryIcon}
          />
          <Text variant="heading2" style={styles.title}>
            {journey.title}
          </Text>
        </View>
        
        <Text variant="body" color="textSecondary" style={styles.description}>
          {journey.description}
        </Text>
        
        <View style={styles.badges}>
          <Badge
            label={`${journey.duration} Days`}
            color={getCategoryColor()}
            icon="calendar"
            style={styles.badge}
          />
          <Badge
            label={getCategoryName()}
            color={getCategoryColor()}
            icon={journey.category === 'jism' ? 'activity' : 
                  journey.category === 'nafs' ? 'wind' : 
                  journey.category === 'aql' ? 'cpu' : 
                  journey.category === 'qalb' ? 'heart' : 
                  journey.category === 'ruh' ? 'star' : 'layers'}
            style={styles.badge}
          />
          {userProgress && (
            <Badge
              label={userProgress.completed ? 'Completed' : 'In Progress'}
              color={userProgress.completed ? colors.success : colors.info}
              icon={userProgress.completed ? 'check-circle' : 'clock'}
              style={styles.badge}
            />
          )}
        </View>
      </View>
    </View>
  );

  // Render the progress section
  const renderProgress = () => (
    <Card style={styles.progressCard}>
      <View style={styles.progressHeader}>
        <Text variant="subtitle">Journey Progress</Text>
        {userProgress && (
          <Text variant="body" color="primary" style={{ fontWeight: '500' }}>
            {userProgress.completed
              ? 'Completed'
              : `Day ${Math.floor((progressPercentage / 100) * journey.duration)} of ${journey.duration}`}
          </Text>
        )}
      </View>
      
      <ProgressBar
        progress={progressPercentage / 100}
        color={getCategoryColor()}
        style={styles.progressBar}
        showLabel
        label={`${progressPercentage.toFixed(0)}%`}
      />
      
      <View style={styles.progressStats}>
        <View style={styles.stat}>
          <Feather name="check-circle" size={16} color={colors.success} style={styles.statIcon} />
          <Text variant="caption" color="textSecondary">
            {completedActivities} of {totalActivities} activities completed
          </Text>
        </View>
        
        {userProgress && userProgress.startDate && (
          <View style={styles.stat}>
            <Feather name="clock" size={16} color={colors.textSecondary} style={styles.statIcon} />
            <Text variant="caption" color="textSecondary">
              Started {new Date(userProgress.startDate).toLocaleDateString()}
            </Text>
          </View>
        )}
      </View>
    </Card>
  );

  // Render the tabs for switching between different views
  const renderTabs = () => (
    <View style={styles.tabs}>
      <TouchableOpacity
        style={[
          styles.tab,
          activeTab === 'overview' && { 
            backgroundColor: getCategoryColor() + '20',
            borderColor: getCategoryColor()
          }
        ]}
        onPress={() => setActiveTab('overview')}
      >
        <Feather
          name="grid"
          size={16}
          color={activeTab === 'overview' ? getCategoryColor() : colors.textSecondary}
          style={styles.tabIcon}
        />
        <Text
          variant="caption"
          color={activeTab === 'overview' ? 'primary' : 'textSecondary'}
        >
          Overview
        </Text>
      </TouchableOpacity>
      
      <TouchableOpacity
        style={[
          styles.tab,
          activeTab === 'activities' && { 
            backgroundColor: getCategoryColor() + '20',
            borderColor: getCategoryColor()
          }
        ]}
        onPress={() => setActiveTab('activities')}
      >
        <Feather
          name="list"
          size={16}
          color={activeTab === 'activities' ? getCategoryColor() : colors.textSecondary}
          style={styles.tabIcon}
        />
        <Text
          variant="caption"
          color={activeTab === 'activities' ? 'primary' : 'textSecondary'}
        >
          Activities
        </Text>
      </TouchableOpacity>
      
      <TouchableOpacity
        style={[
          styles.tab,
          activeTab === 'resources' && { 
            backgroundColor: getCategoryColor() + '20',
            borderColor: getCategoryColor() 
          }
        ]}
        onPress={() => setActiveTab('resources')}
      >
        <Feather
          name="book-open"
          size={16}
          color={activeTab === 'resources' ? getCategoryColor() : colors.textSecondary}
          style={styles.tabIcon}
        />
        <Text
          variant="caption"
          color={activeTab === 'resources' ? 'primary' : 'textSecondary'}
        >
          Resources
        </Text>
      </TouchableOpacity>
    </View>
  );

  // Render the overview tab content
  const renderOverviewTab = () => (
    <View style={styles.tabContent}>
      {renderProgress()}
      
      <Card style={styles.summaryCard}>
        <Text variant="subtitle">Journey Summary</Text>
        <View style={styles.summaryItem}>
          <Feather name="calendar" size={16} color={colors.textSecondary} style={styles.summaryIcon} />
          <Text variant="body">Duration: {journey.duration} days</Text>
        </View>
        <View style={styles.summaryItem}>
          <Feather name="clock" size={16} color={colors.textSecondary} style={styles.summaryIcon} />
          <Text variant="body">Daily commitment: {journey.dailyCommitmentMinutes || 15} minutes</Text>
        </View>
        <View style={styles.summaryItem}>
          <Feather name="target" size={16} color={colors.textSecondary} style={styles.summaryIcon} />
          <Text variant="body">Focus: {getCategoryName()}</Text>
        </View>
        <View style={styles.summaryItem}>
          <Feather name="list" size={16} color={colors.textSecondary} style={styles.summaryIcon} />
          <Text variant="body">Activities: {totalActivities}</Text>
        </View>
      </Card>
      
      {activities.length > 0 && (
        <Card style={styles.nextActivityCard}>
          <Text variant="subtitle">Next Activity</Text>
          {activities.find(a => !a.completed) ? (
            <View style={styles.nextActivity}>
              <View style={styles.activityHeader}>
                <Text variant="body" style={{ fontWeight: '500' }}>
                  {activities.find(a => !a.completed)?.title}
                </Text>
                <Badge
                  label={activities.find(a => !a.completed)?.type || 'Practice'}
                  color={getCategoryColor()}
                  style={{ padding: 4 }}
                />
              </View>
              <Text variant="body" color="textSecondary" style={styles.activityDescription}>
                {activities.find(a => !a.completed)?.description || 'No description available.'}
              </Text>
              <Button
                title="Start Activity"
                variant="primary"
                size="medium"
                icon="play"
                onPress={() => {
                  const nextActivity = activities.find(a => !a.completed);
                  if (nextActivity) {
                    // Navigate to activity detail
                    navigation.navigate('ActivityDetail', {
                      journeyId,
                      activityId: nextActivity.id
                    });
                  }
                }}
                style={{ backgroundColor: getCategoryColor() }}
              />
            </View>
          ) : (
            <Text variant="body" color="textSecondary">
              You've completed all activities for this journey!
            </Text>
          )}
        </Card>
      )}
    </View>
  );

  // Render the activities tab content
  const renderActivitiesTab = () => (
    <View style={styles.tabContent}>
      <Text variant="subtitle" style={styles.sectionTitle}>All Activities</Text>
      
      {activities.length === 0 ? (
        <Text variant="body" color="textSecondary">
          No activities available for this journey.
        </Text>
      ) : (
        activities.map((activity) => (
          <Card key={activity.id} style={styles.activityCard}>
            <View style={styles.activityCardContent}>
              <View style={styles.activityHeader}>
                <View style={styles.activityTitle}>
                  {activity.completed && (
                    <Feather name="check-circle" size={16} color={colors.success} style={styles.completedIcon} />
                  )}
                  <Text variant="body" style={{ fontWeight: '500' }}>
                    {activity.title}
                  </Text>
                </View>
                <Badge
                  label={activity.type || 'Practice'}
                  color={getCategoryColor()}
                  style={{ padding: 4 }}
                />
              </View>
              
              <Text variant="body" color="textSecondary" style={styles.activityDescription}>
                {activity.description}
              </Text>
              
              <View style={styles.activityFooter}>
                {activity.completed ? (
                  <View style={styles.completedContainer}>
                    <Text variant="caption" color="success">
                      Completed {activity.completionDate && new Date(activity.completionDate).toLocaleDateString()}
                    </Text>
                  </View>
                ) : (
                  <Button
                    title="Complete"
                    variant="outline"
                    size="small"
                    icon="check"
                    onPress={() => handleCompleteActivity(activity.id)}
                    style={styles.completeButton}
                  />
                )}
                
                <TouchableOpacity
                  style={styles.detailsButton}
                  onPress={() => {
                    // Navigate to activity detail
                    navigation.navigate('ActivityDetail', {
                      journeyId,
                      activityId: activity.id
                    });
                  }}
                >
                  <Text variant="caption" color="primary">View Details</Text>
                  <Feather name="chevron-right" size={16} color={colors.primary} />
                </TouchableOpacity>
              </View>
            </View>
          </Card>
        ))
      )}
    </View>
  );

  // Render the resources tab content
  const renderResourcesTab = () => (
    <View style={styles.tabContent}>
      <Text variant="subtitle" style={styles.sectionTitle}>Recommended Resources</Text>
      
      <Card style={styles.resourceCard}>
        <Image
          source={{ uri: 'https://images.pexels.com/photos/1028599/pexels-photo-1028599.jpeg' }}
          style={styles.resourceImage}
        />
        <View style={styles.resourceContent}>
          <Text variant="body" style={{ fontWeight: '500' }}>Understanding the {getCategoryName()}</Text>
          <Text variant="caption" color="textSecondary">
            Article • 5 min read
          </Text>
          <Text variant="body" color="textSecondary" style={styles.resourceDescription}>
            Learn about the spiritual significance of the {getCategoryName().toLowerCase()} and how to nurture it.
          </Text>
          <Button
            title="Read Article"
            variant="outline"
            size="small"
            icon="book-open"
            onPress={() => {}}
            style={styles.resourceButton}
          />
        </View>
      </Card>
      
      <Card style={styles.resourceCard}>
        <Image
          source={{ uri: 'https://images.pexels.com/photos/3094230/pexels-photo-3094230.jpeg' }}
          style={styles.resourceImage}
        />
        <View style={styles.resourceContent}>
          <Text variant="body" style={{ fontWeight: '500' }}>Guided Meditation for {getCategoryName()}</Text>
          <Text variant="caption" color="textSecondary">
            Audio • 10 min
          </Text>
          <Text variant="body" color="textSecondary" style={styles.resourceDescription}>
            A peaceful guided meditation to help you connect with your {getCategoryName().toLowerCase()}.
          </Text>
          <Button
            title="Listen Now"
            variant="outline"
            size="small"
            icon="headphones"
            onPress={() => {}}
            style={styles.resourceButton}
          />
        </View>
      </Card>
      
      <Card style={styles.resourceCard}>
        <Image
          source={{ uri: 'https://images.pexels.com/photos/301926/pexels-photo-301926.jpeg' }}
          style={styles.resourceImage}
        />
        <View style={styles.resourceContent}>
          <Text variant="body" style={{ fontWeight: '500' }}>Dhikr Practices for Inner Peace</Text>
          <Text variant="caption" color="textSecondary">
            Video • 7 min
          </Text>
          <Text variant="body" color="textSecondary" style={styles.resourceDescription}>
            Learn traditional dhikr practices to cultivate inner peace and connect with Allah.
          </Text>
          <Button
            title="Watch Video"
            variant="outline"
            size="small"
            icon="video"
            onPress={() => {}}
            style={styles.resourceButton}
          />
        </View>
      </Card>
    </View>
  );

  return (
    <View style={[styles.container, { paddingTop: insets.top }]}>
      <ScrollView showsVerticalScrollIndicator={false}>
        {renderHeader()}
        {renderTabs()}
        
        {activeTab === 'overview' && renderOverviewTab()}
        {activeTab === 'activities' && renderActivitiesTab()}
        {activeTab === 'resources' && renderResourcesTab()}
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  loadingText: {
    marginTop: Theme.spacing.m,
    textAlign: 'center',
  },
  header: {
    padding: Theme.spacing.m,
  },
  backButton: {
    marginBottom: Theme.spacing.m,
  },
  journeyDetails: {},
  titleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Theme.spacing.s,
  },
  categoryIcon: {
    marginRight: Theme.spacing.s,
  },
  title: {
    flex: 1,
  },
  description: {
    marginBottom: Theme.spacing.m,
  },
  badges: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  badge: {
    marginRight: Theme.spacing.s,
    marginBottom: Theme.spacing.s,
  },
  tabs: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: Theme.spacing.m,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  tab: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: Theme.spacing.s,
    paddingHorizontal: Theme.spacing.m,
    borderRadius: Theme.borderRadius.pill,
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  tabIcon: {
    marginRight: Theme.spacing.xs,
  },
  tabContent: {
    padding: Theme.spacing.m,
  },
  progressCard: {
    marginBottom: Theme.spacing.m,
  },
  progressHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Theme.spacing.s,
  },
  progressBar: {
    height: 10,
    borderRadius: 5,
    marginBottom: Theme.spacing.s,
  },
  progressStats: {
    marginTop: Theme.spacing.s,
  },
  stat: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Theme.spacing.xs,
  },
  statIcon: {
    marginRight: Theme.spacing.xs,
  },
  summaryCard: {
    marginBottom: Theme.spacing.m,
  },
  summaryItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: Theme.spacing.s,
  },
  summaryIcon: {
    marginRight: Theme.spacing.s,
  },
  nextActivityCard: {
    marginBottom: Theme.spacing.m,
  },
  nextActivity: {
    marginTop: Theme.spacing.s,
  },
  activityHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Theme.spacing.s,
  },
  activityDescription: {
    marginBottom: Theme.spacing.m,
  },
  sectionTitle: {
    marginBottom: Theme.spacing.m,
  },
  activityCard: {
    marginBottom: Theme.spacing.m,
  },
  activityCardContent: {
    padding: Theme.spacing.s,
  },
  activityTitle: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  completedIcon: {
    marginRight: Theme.spacing.xs,
  },
  activityFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: Theme.spacing.m,
  },
  completedContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  completeButton: {
    minWidth: 100,
  },
  detailsButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  resourceCard: {
    marginBottom: Theme.spacing.m,
    overflow: 'hidden',
  },
  resourceImage: {
    width: '100%',
    height: 120,
  },
  resourceContent: {
    padding: Theme.spacing.m,
  },
  resourceDescription: {
    marginVertical: Theme.spacing.s,
  },
  resourceButton: {
    alignSelf: 'flex-start',
    marginTop: Theme.spacing.s,
  },
});

