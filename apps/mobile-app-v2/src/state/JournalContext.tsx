import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import journalService from '../../services/api/JournalService';
import * as Types from '../../services/api/types';

// Define the shape of our context
interface JournalContextType {
  // State
  entries: Types.JournalEntry[];
  isLoading: boolean;
  error: string | null;
  analytics: Types.JournalAnalytics | null;
  insights: { 
    category: string;
    title: string;
    description: string;
    relatedEntries: string[];
    suggestedActions?: string[];
  }[] | null;
  reflectionPrompts: Types.ReflectionPrompt[];
  
  // CRUD operations
  createEntry: (entry: {
    title: string;
    content: string;
    entryType: Types.EntryType;
    mood?: string;
    tags?: string[];
    layers?: Types.SoulLayer[];
  }) => Promise<Types.JournalEntry>;
  
  getEntries: (params?: {
    entryType?: Types.EntryType;
    mood?: string;
    tags?: string[];
    layers?: Types.SoulLayer[];
    startDate?: string;
    endDate?: string;
    page?: number;
    limit?: number;
    sortBy?: 'date' | 'mood' | 'type';
    sortDirection?: 'asc' | 'desc';
  }) => Promise<void>;
  
  getEntry: (entryId: string) => Promise<Types.JournalEntry>;
  
  updateEntry: (
    entryId: string,
    updates: {
      title?: string;
      content?: string;
      mood?: string;
      tags?: string[];
      layers?: Types.SoulLayer[];
    }
  ) => Promise<Types.JournalEntry>;
  
  deleteEntry: (entryId: string) => Promise<boolean>;
  
  // Additional functionality
  getAnalytics: (timeframe?: '7days' | '30days' | '90days' | 'all') => Promise<void>;
  generateInsights: () => Promise<void>;
  getReflectionPrompts: (params?: {
    category?: string;
    layer?: Types.SoulLayer;
  }) => Promise<void>;
  
  saveReflection: (reflection: {
    promptId: string;
    content: string;
    mood?: string;
    layers?: Types.SoulLayer[];
  }) => Promise<Types.JournalEntry>;
  
  recordJourneyMilestone: (milestone: {
    journeyId: string;
    milestoneId: string;
    title: string;
    reflection?: string;
    mood?: string;
    tags?: string[];
    layers?: Types.SoulLayer[];
  }) => Promise<Types.JournalEntry>;
  
  exportJournal: (params: {
    format: 'pdf' | 'json' | 'text';
    startDate?: string;
    endDate?: string;
    includeAnalytics?: boolean;
  }) => Promise<{
    exportUrl: string;
    expiresAt: string;
  }>;
  
  // Utility functions
  clearError: () => void;
  reset: () => void;
}

// Create the context with a default value
const JournalContext = createContext<JournalContextType | undefined>(undefined);

// Provider component
interface JournalProviderProps {
  children: ReactNode;
}

export const JournalProvider: React.FC<JournalProviderProps> = ({ children }) => {
  // State
  const [entries, setEntries] = useState<Types.JournalEntry[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [analytics, setAnalytics] = useState<Types.JournalAnalytics | null>(null);
  const [insights, setInsights] = useState<{
    category: string;
    title: string;
    description: string;
    relatedEntries: string[];
    suggestedActions?: string[];
  }[] | null>(null);
  const [reflectionPrompts, setReflectionPrompts] = useState<Types.ReflectionPrompt[]>([]);

  // Load initial data
  useEffect(() => {
    // Load reflection prompts on mount
    getReflectionPrompts();
    
    // Get initial entries
    getEntries();
  }, []);

  // CRUD operations
  const createEntry = async (entry: {
    title: string;
    content: string;
    entryType: Types.EntryType;
    mood?: string;
    tags?: string[];
    layers?: Types.SoulLayer[];
  }): Promise<Types.JournalEntry> => {
    setIsLoading(true);
    setError(null);
    
    try {
      const newEntry = await journalService.createEntry(entry);
      setEntries(prevEntries => [newEntry, ...prevEntries]);
      return newEntry;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to create journal entry';
      setError(errorMessage);
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  const getEntries = async (params?: {
    entryType?: Types.EntryType;
    mood?: string;
    tags?: string[];
    layers?: Types.SoulLayer[];
    startDate?: string;
    endDate?: string;
    page?: number;
    limit?: number;
    sortBy?: 'date' | 'mood' | 'type';
    sortDirection?: 'asc' | 'desc';
  }): Promise<void> => {
    setIsLoading(true);
    setError(null);
    
    try {
      const result = await journalService.getEntries(params);
      setEntries(result.entries);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to retrieve journal entries';
      setError(errorMessage);
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  const getEntry = async (entryId: string): Promise<Types.JournalEntry> => {
    setIsLoading(true);
    setError(null);
    
    try {
      return await journalService.getEntry(entryId);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to retrieve journal entry';
      setError(errorMessage);
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  const updateEntry = async (
    entryId: string,
    updates: {
      title?: string;
      content?: string;
      mood?: string;
      tags?: string[];
      layers?: Types.SoulLayer[];
    }
  ): Promise<Types.JournalEntry> => {
    setIsLoading(true);
    setError(null);
    
    try {
      const updatedEntry = await journalService.updateEntry(entryId, updates);
      
      // Update the entry in the local state
      setEntries(prevEntries => 
        prevEntries.map(entry => 
          entry.id === entryId ? updatedEntry : entry
        )
      );
      
      return updatedEntry;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update journal entry';
      setError(errorMessage);
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  const deleteEntry = async (entryId: string): Promise<boolean> => {
    setIsLoading(true);
    setError(null);
    
    try {
      const result = await journalService.deleteEntry(entryId);
      
      if (result.success) {
        // Remove the entry from the local state
        setEntries(prevEntries => 
          prevEntries.filter(entry => entry.id !== entryId)
        );
      }
      
      return result.success;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete journal entry';
      setError(errorMessage);
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  // Additional functionality
  const getAnalytics = async (timeframe: '7days' | '30days' | '90days' | 'all' = '30days'): Promise<void> => {
    setIsLoading(true);
    setError(null);
    
    try {
      const result = await journalService.getAnalytics(timeframe);
      setAnalytics(result);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to retrieve analytics';
      setError(errorMessage);
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  const generateInsights = async (): Promise<void> => {
    setIsLoading(true);
    setError(null);
    
    try {
      const result = await journalService.generateInsights();
      setInsights(result.insights);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to generate insights';
      setError(errorMessage);
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  const getReflectionPrompts = async (params?: {
    category?: string;
    layer?: Types.SoulLayer;
  }): Promise<void> => {
    setIsLoading(true);
    setError(null);
    
    try {
      const result = await journalService.getReflectionPrompts(params);
      setReflectionPrompts(result.prompts);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to retrieve reflection prompts';
      setError(errorMessage);
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  const saveReflection = async (reflection: {
    promptId: string;
    content: string;
    mood?: string;
    layers?: Types.SoulLayer[];
  }): Promise<Types.JournalEntry> => {
    setIsLoading(true);
    setError(null);
    
    try {
      const result = await journalService.saveReflection(reflection);
      
      // Add the new entry to the local state
      setEntries(prevEntries => [result.entry, ...prevEntries]);
      
      return result.entry;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to save reflection';
      setError(errorMessage);
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  const recordJourneyMilestone = async (milestone: {
    journeyId: string;
    milestoneId: string;
    title: string;
    reflection?: string;
    mood?: string;
    tags?: string[];
    layers?: Types.SoulLayer[];
  }): Promise<Types.JournalEntry> => {
    setIsLoading(true);
    setError(null);
    
    try {
      const result = await journalService.recordJourneyMilestone(milestone);
      
      // Add the new entry to the local state
      setEntries(prevEntries => [result, ...prevEntries]);
      
      return result;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to record journey milestone';
      setError(errorMessage);
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  const exportJournal = async (params: {
    format: 'pdf' | 'json' | 'text';
    startDate?: string;
    endDate?: string;
    includeAnalytics?: boolean;
  }): Promise<{
    exportUrl: string;
    expiresAt: string;
  }> => {
    setIsLoading(true);
    setError(null);
    
    try {
      return await journalService.exportJournal(params);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to export journal';
      setError(errorMessage);
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  // Utility functions
  const clearError = (): void => {
    setError(null);
  };

  const reset = (): void => {
    setEntries([]);
    setAnalytics(null);
    setInsights(null);
    setError(null);
  };

  // Context value
  const contextValue: JournalContextType = {
    // State
    entries,
    isLoading,
    error,
    analytics,
    insights,
    reflectionPrompts,
    
    // CRUD operations
    createEntry,
    getEntries,
    getEntry,
    updateEntry,
    deleteEntry,
    
    // Additional functionality
    getAnalytics,
    generateInsights,
    getReflectionPrompts,
    saveReflection,
    recordJourneyMilestone,
    exportJournal,
    
    // Utility functions
    clearError,
    reset,
  };

  return (
    <JournalContext.Provider value={contextValue}>
      {children}
    </JournalContext.Provider>
  );
};

// Custom hook for using the Journal context
export const useJournal = (): JournalContextType => {
  const context = useContext(JournalContext);
  
  if (context === undefined) {
    throw new Error('useJournal must be used within a JournalProvider');
  }
  
  return context;
};

