import React, { createContext, useContext, useEffect, useReducer } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { UserState, ActionType, UserPreferences } from '../types';
import { useAppState } from './AppStateContext';

// Initial state
const initialUserPreferences: UserPreferences = {
  theme: 'system',
  notificationsEnabled: true,
  reminderTime: '09:00',
  language: 'en',
};

const initialUserState: UserState = {
  userId: null,
  username: null,
  profileComplete: false,
  isAuthenticated: false,
  preferences: initialUserPreferences,
};

// Create context
const UserContext = createContext<{
  state: UserState;
  dispatch: React.Dispatch<ActionType>;
  updatePreferences: (preferences: Partial<UserPreferences>) => void;
  login: (userId: string, username: string) => void;
  logout: () => void;
  completeProfile: () => void;
}>({
  state: initialUserState,
  dispatch: () => null,
  updatePreferences: () => null,
  login: () => null,
  logout: () => null,
  completeProfile: () => null,
});

// Reducer function
function userReducer(state: UserState, action: ActionType): UserState {
  switch (action.type) {
    case 'USER_LOGIN_SUCCESS':
      return {
        ...state,
        userId: action.payload.userId,
        username: action.payload.username,
        isAuthenticated: true,
      };
    case 'USER_LOGOUT':
      return {
        ...initialUserState,
      };
    case 'USER_UPDATE_PREFERENCES':
      return {
        ...state,
        preferences: {
          ...state.preferences,
          ...action.payload,
        },
      };
    case 'USER_COMPLETE_PROFILE':
      return {
        ...state,
        profileComplete: true,
      };
    default:
      return state;
  }
}

// Storage keys
const USER_STORAGE_KEY = 'qalb_user_data';
const USER_PREFERENCES_KEY = 'qalb_user_preferences';

// Provider component
export const UserProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [state, dispatch] = useReducer(userReducer, initialUserState);
  const { state: appState, dispatch: appDispatch } = useAppState();

  // Load user data from storage on mount
  useEffect(() => {
    const loadUserData = async () => {
      try {
        const userData = await AsyncStorage.getItem(USER_STORAGE_KEY);
        const userPreferences = await AsyncStorage.getItem(USER_PREFERENCES_KEY);
        
        if (userData) {
          const parsedUserData = JSON.parse(userData);
          
          if (parsedUserData.userId && parsedUserData.username) {
            dispatch({
              type: 'USER_LOGIN_SUCCESS',
              payload: {
                userId: parsedUserData.userId,
                username: parsedUserData.username,
              },
            });
          }
          
          if (parsedUserData.profileComplete) {
            dispatch({ type: 'USER_COMPLETE_PROFILE' });
          }
        }
        
        if (userPreferences) {
          const parsedPreferences = JSON.parse(userPreferences);
          dispatch({
            type: 'USER_UPDATE_PREFERENCES',
            payload: parsedPreferences,
          });
        }
      } catch (error) {
        console.error('Failed to load user data from storage:', error);
      }
    };
    
    loadUserData();
  }, []);

  // Save user data to storage when it changes
  useEffect(() => {
    const saveUserData = async () => {
      try {
        if (state.isAuthenticated) {
          await AsyncStorage.setItem(
            USER_STORAGE_KEY,
            JSON.stringify({
              userId: state.userId,
              username: state.username,
              profileComplete: state.profileComplete,
            })
          );
        } else {
          // Clear user data on logout
          await AsyncStorage.removeItem(USER_STORAGE_KEY);
        }
      } catch (error) {
        console.error('Failed to save user data to storage:', error);
      }
    };
    
    saveUserData();
  }, [state.userId, state.username, state.profileComplete, state.isAuthenticated]);

  // Save user preferences to storage when they change
  useEffect(() => {
    const saveUserPreferences = async () => {
      try {
        await AsyncStorage.setItem(
          USER_PREFERENCES_KEY,
          JSON.stringify(state.preferences)
        );
      } catch (error) {
        console.error('Failed to save user preferences to storage:', error);
      }
    };
    
    saveUserPreferences();
  }, [state.preferences]);

  // Helper functions for context consumers
  const updatePreferences = (preferences: Partial<UserPreferences>) => {
    dispatch({ type: 'USER_UPDATE_PREFERENCES', payload: preferences });
  };

  const login = (userId: string, username: string) => {
    dispatch({
      type: 'USER_LOGIN_SUCCESS',
      payload: { userId, username },
    });
    
    // If we're offline, queue this action for syncing
    if (!appState.isOnline) {
      appDispatch({
        type: 'APP_ADD_SYNC_ACTION',
        payload: {
          type: 'USER_LOGIN',
          payload: { userId, username },
        },
      });
    }
  };

  const logout = () => {
    dispatch({ type: 'USER_LOGOUT' });
  };

  const completeProfile = () => {
    dispatch({ type: 'USER_COMPLETE_PROFILE' });
    
    // If we're offline, queue this action for syncing
    if (!appState.isOnline) {
      appDispatch({
        type: 'APP_ADD_SYNC_ACTION',
        payload: {
          type: 'USER_COMPLETE_PROFILE',
          payload: {},
        },
      });
    }
  };

  return (
    <UserContext.Provider
      value={{
        state,
        dispatch,
        updatePreferences,
        login,
        logout,
        completeProfile,
      }}
    >
      {children}
    </UserContext.Provider>
  );
};

// Custom hook for using user context
export const useUser = () => {
  const context = useContext(UserContext);
  if (!context) {
    throw new Error('useUser must be used within a UserProvider');
  }
  return context;
};

