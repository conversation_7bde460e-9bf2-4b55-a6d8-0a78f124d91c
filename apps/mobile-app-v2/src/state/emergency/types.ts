import {
  EmergencyContact,
  EmergencyResource,
} from "../../../services/api/types";

export interface EmergencyState {
  contacts: EmergencyContact[];
  resources: EmergencyResource[];
  hasCompletedSetup: boolean;
  loading: boolean;
  error: string | null;
}

export type EmergencyAction =
  | { type: "EMERGENCY_FETCH_REQUEST" }
  | {
      type: "EMERGENCY_FETCH_SUCCESS";
      payload: { contacts: EmergencyContact[]; resources: EmergencyResource[] };
    }
  | { type: "EMERGENCY_FETCH_FAILURE"; payload: string }
  | { type: "EMERGENCY_ADD_CONTACT"; payload: EmergencyContact }
  | { type: "EMERGENCY_UPDATE_CONTACT"; payload: EmergencyContact }
  | { type: "EMERGENCY_DELETE_CONTACT"; payload: string } // contactId
  | { type: "EMERGENCY_COMPLETE_SETUP" };
