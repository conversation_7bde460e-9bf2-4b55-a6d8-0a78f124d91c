import React from 'react';

import { StyleSheet, ScrollView, TouchableOpacity } from 'react-native';

import { colors } from '../constants/Colors';
import Theme from '../constants/Theme';

import { Badge } from './ui/Badge';
import { Text } from './ui/Text';
import { View } from './ui/View';

interface EmotionPickerProps {
  selectedEmotions: string[];
  onSelectEmotion: (emotion: string) => void;
}

// Common emotions for journal entries
const COMMON_EMOTIONS = [
  'peaceful',
  'calm',
  'anxious',
  'grateful',
  'sad',
  'hopeful',
  'frustrated',
  'content',
  'reflective',
  'confused',
  'inspired',
  'motivated',
  'discouraged',
  'angry',
  'fearful',
  'connected',
  'disconnected',
  'joyful',
  'energized',
  'tired',
  'present',
  'distracted',
  'focused',
  'overwhelmed',
  'hopeless',
  'liberated',
  'humble',
  'proud',
];

export const EmotionPicker = ({
  selectedEmotions,
  onSelectEmotion,
}: EmotionPickerProps) => {
  // Toggle emotion selection
  const toggleEmotion = (emotion: string) => {
    onSelectEmotion(emotion);
  };

  // Check if emotion is selected
  const isSelected = (emotion: string) => {
    return selectedEmotions.includes(emotion);
  };

  return (
    <View style={styles.container}>
      <Text variant="subtitle" style={styles.title}>
        How are you feeling?
      </Text>

      <Text variant="caption" color="textSecondary" style={styles.subtitle}>
        Select all emotions that apply.
      </Text>

      <ScrollView
        style={styles.emotionsContainer}
        contentContainerStyle={styles.emotionsContent}
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.emotionsGrid}>
          {COMMON_EMOTIONS.map((emotion) => (
            <TouchableOpacity
              key={emotion}
              style={styles.emotionItem}
              onPress={() => toggleEmotion(emotion)}
            >
              <Badge
                label={emotion}
                style={[
                  styles.emotionBadge,
                  {
                    backgroundColor: isSelected(emotion)
                      ? colors.primary + '30'
                      : colors.background,
                    borderColor: isSelected(emotion)
                      ? colors.primary
                      : colors.border,
                  },
                ]}
              />
            </TouchableOpacity>
          ))}
        </View>
      </ScrollView>

      {selectedEmotions.length > 0 && (
        <View style={styles.selectedContainer}>
          <Text variant="caption" color="textSecondary">
            Selected: {selectedEmotions.length}
          </Text>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: Theme.spacing.m,
  },
  title: {
    marginBottom: Theme.spacing.xs,
  },
  subtitle: {
    marginBottom: Theme.spacing.s,
  },
  emotionsContainer: {
    maxHeight: 150,
  },
  emotionsContent: {
    paddingBottom: Theme.spacing.s,
  },
  emotionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  emotionItem: {
    marginRight: Theme.spacing.xs,
    marginBottom: Theme.spacing.xs,
  },
  emotionBadge: {
    borderWidth: 1,
  },
  selectedContainer: {
    alignItems: 'flex-end',
    marginTop: Theme.spacing.xs,
  },
});
