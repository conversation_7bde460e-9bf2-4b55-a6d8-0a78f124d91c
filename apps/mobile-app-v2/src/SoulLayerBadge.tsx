import React from 'react';

import { StyleSheet } from 'react-native';

import { SoulLayer } from '../types';

import { Badge } from './ui/Badge';

type SoulLayerBadgeProps = {
  layer: SoulLayer;
  size?: 'small' | 'medium' | 'large';
};

export function SoulLayerBadge({ layer, size = 'medium' }: SoulLayerBadgeProps) {
  // Map soul layers to appropriate variants and labels
  const getLayerInfo = (layer: SoulLayer) => {
    switch (layer) {
      case 'jism':
        return { variant: 'info' as const, label: 'Jism (Body)' };
      case 'nafs':
        return { variant: 'warning' as const, label: 'Nafs (Self)' };
      case 'aql':
        return { variant: 'secondary' as const, label: 'Aql (Intellect)' };
      case 'qalb':
        return { variant: 'primary' as const, label: 'Qalb (Heart)' };
      case 'ruh':
        return { variant: 'success' as const, label: 'Ruh (Spirit)' };
      default:
        return { variant: 'primary' as const, label: layer };
    }
  };

  const { variant, label } = getLayerInfo(layer);

  return (
    <Badge
      variant={variant}
      label={label}
      size={size}
      style={styles.badge}
    />
  );
}

const styles = StyleSheet.create({
  badge: {
    marginRight: 8,
    marginBottom: 8,
  },
});
