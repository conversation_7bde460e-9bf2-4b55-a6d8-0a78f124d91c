import React, { useEffect, useRef, useState } from 'react';

import { Animated, Easing, View as RNView, StyleSheet } from 'react-native';

import { Feather } from '@expo/vector-icons';
import * as Haptics from 'expo-haptics';
import { LinearGradient } from 'expo-linear-gradient';

import { colors } from '../constants/Colors';
import Theme from '../constants/Theme';

import { Text } from './ui/Text';
import { View } from './ui/View';

interface BreathingGuideProps {
  inhaleTime: number; // seconds
  holdTime: number; // seconds
  exhaleTime: number; // seconds
  repetitions: number;
  onComplete: () => void;
}

type BreathingState = 'inhale' | 'hold' | 'exhale' | 'complete';

export const BreathingGuide = ({
  inhaleTime,
  holdTime,
  exhaleTime,
  repetitions,
  onComplete,
}: BreathingGuideProps) => {
  // Animation values
  const breathAnimation = useRef(new Animated.Value(0)).current;
  const fadeAnimation = useRef(new Animated.Value(1)).current;

  // State
  const [breathingState, setBreathingState] =
    useState<BreathingState>('inhale');
  const [currentCount, setCurrentCount] = useState(1);
  const [timeLeft, setTimeLeft] = useState(inhaleTime);
  // const [totalDuration] = useState(inhaleTime + holdTime + exhaleTime);

  // Timer reference
  const timerRef = useRef<ReturnType<typeof setTimeout> | null>(null);

  // Set up breathing animation
  useEffect(() => {
    if (breathingState === 'complete') {
      fadeOut();
      onComplete();
      return;
    }

    if (currentCount > repetitions) {
      setBreathingState('complete');
      return;
    }

    let duration = 0;
    let toValue = 0;
    let onCompleteAction = () => {
      // Default empty action
    };

    // Configure animation based on breathing state
    switch (breathingState) {
      case 'inhale':
        duration = inhaleTime * 1000;
        toValue = 1;
        onCompleteAction = () => {
          Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
          setBreathingState('hold');
          setTimeLeft(holdTime);
        };
        break;
      case 'hold':
        duration = holdTime * 1000;
        toValue = 1; // maintain size
        onCompleteAction = () => {
          Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
          setBreathingState('exhale');
          setTimeLeft(exhaleTime);
        };
        break;
      case 'exhale':
        duration = exhaleTime * 1000;
        toValue = 0;
        onCompleteAction = () => {
          Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
          setCurrentCount((prev) => prev + 1);
          setBreathingState('inhale');
          setTimeLeft(inhaleTime);
        };
        break;
      default:
        break;
    }

    // Start animation
    Animated.timing(breathAnimation, {
      toValue,
      duration,
      easing: Easing.inOut(Easing.cubic),
      useNativeDriver: false,
    }).start(onCompleteAction);

    // Set up timer
    if (timerRef.current) {
      clearInterval(timerRef.current);
    }

    timerRef.current = setInterval(() => {
      setTimeLeft((prev) => {
        if (prev <= 1) {
          clearInterval(timerRef.current!);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, [
    breathingState,
    currentCount,
    inhaleTime,
    holdTime,
    exhaleTime,
    repetitions,
    breathAnimation,
    onComplete,
  ]);

  // Fade out animation when complete
  const fadeOut = () => {
    Animated.timing(fadeAnimation, {
      toValue: 0,
      duration: 1000,
      useNativeDriver: false,
    }).start();
  };

  // Calculate the size of the breathing circle
  const circleSize = breathAnimation.interpolate({
    inputRange: [0, 1],
    outputRange: [150, 280],
  });

  // Get instruction text based on breathing state
  const getInstructionText = () => {
    switch (breathingState) {
      case 'inhale':
        return 'Inhale';
      case 'hold':
        return 'Hold';
      case 'exhale':
        return 'Exhale';
      case 'complete':
        return 'Complete';
      default:
        return '';
    }
  };

  // Calculate progress percentage
  const getProgress = () => {
    let totalTime = 0;
    let elapsedTime = 0;

    switch (breathingState) {
      case 'inhale':
        totalTime = inhaleTime;
        elapsedTime = inhaleTime - timeLeft;
        break;
      case 'hold':
        totalTime = holdTime;
        elapsedTime = holdTime - timeLeft;
        break;
      case 'exhale':
        totalTime = exhaleTime;
        elapsedTime = exhaleTime - timeLeft;
        break;
      default:
        break;
    }

    return Math.round((elapsedTime / totalTime) * 100);
  };

  return (
    <Animated.View style={[styles.container, { opacity: fadeAnimation }]}>
      <Text variant="heading3" style={styles.title}>
        Guided Breathing
      </Text>

      <View style={styles.countContainer}>
        <Text variant="body" color="textSecondary">
          {currentCount} of {repetitions}
        </Text>
      </View>

      <RNView style={styles.circleContainer}>
        <Animated.View
          style={[
            styles.breathCircle,
            {
              width: circleSize,
              height: circleSize,
              borderRadius: circleSize,
              transform: [{ scale: breathAnimation }],
            },
          ]}
        >
          <LinearGradient
            colors={[colors.primary + '80', colors.accent + '80']}
            style={styles.gradient}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
          />
        </Animated.View>

        <View style={styles.instructionContainer}>
          <Text
            variant="heading2"
            color="surface"
            style={styles.instructionText}
          >
            {getInstructionText()}
          </Text>
          <Text variant="heading3" color="surface">
            {timeLeft}
          </Text>
        </View>
      </RNView>

      <View style={styles.progressTextContainer}>
        <Text variant="body" color="textSecondary">
          {getProgress()}% Complete
        </Text>
      </View>

      <View style={styles.tipsContainer}>
        <Feather name="info" size={16} style={styles.infoIcon} />
        <Text variant="caption" color="textSecondary" style={styles.tipText}>
          Tip: Focus on your breathing and try to clear your mind.
        </Text>
      </View>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: Theme.spacing.m,
  },
  title: {
    marginBottom: Theme.spacing.m,
    textAlign: 'center',
  },
  countContainer: {
    marginBottom: Theme.spacing.m,
  },
  circleContainer: {
    position: 'relative',
    width: 300,
    height: 300,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: Theme.spacing.m,
  },
  breathCircle: {
    position: 'absolute',
    overflow: 'hidden',
  },
  gradient: {
    width: '100%',
    height: '100%',
    borderRadius: 9999,
  },
  instructionContainer: {
    alignItems: 'center',
  },
  instructionText: {
    textShadowColor: 'rgba(0, 0, 0, 0.5)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 2,
  },
  progressTextContainer: {
    marginBottom: Theme.spacing.m,
  },
  tipsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.05)',
    padding: Theme.spacing.s,
    borderRadius: Theme.borderRadius.small,
  },
  infoIcon: {
    marginRight: Theme.spacing.xs,
  },
  tipText: {
    flex: 1,
  },
});
