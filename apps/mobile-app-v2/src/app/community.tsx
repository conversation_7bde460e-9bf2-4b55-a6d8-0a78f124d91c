import { Feather } from '@expo/vector-icons';
import { Stack, useRouter } from 'expo-router';
import React from 'react';
import { Image, StyleSheet } from 'react-native';
import { ScrollView } from 'react-native-gesture-handler';

import { Button } from '../components/ui/Button';
import { Card } from '../components/ui/Card';
import { Text } from '../components/ui/Text';
import { View } from '../components/ui/View';
import { colors } from '../constants/Colors';
import Theme from '../constants/Theme';

export default function CommunityScreen() {
  const router = useRouter();

  const communities = [
    {
      id: '1',
      name: 'Healing Hearts Circle',
      members: 125,
      description:
        'A supportive space for those healing from anxiety and depression through Islamic principles.',
      imageUrl:
        'https://images.pexels.com/photos/1261728/pexels-photo-1261728.jpeg',
    },
    {
      id: '2',
      name: 'Quranic Healing Group',
      members: 89,
      description:
        'Exploring healing through Quranic verses and prophetic medicine.',
      imageUrl:
        'https://images.pexels.com/photos/1261728/pexels-photo-1261728.jpeg',
    },
    {
      id: '3',
      name: 'Spiritual Growth Circle',
      members: 103,
      description:
        'For Muslims seeking spiritual growth and a deeper connection with Allah.',
      imageUrl:
        'https://images.pexels.com/photos/1261728/pexels-photo-1261728.jpeg',
    },
  ];

  return (
    <>
      <Stack.Screen options={{ title: 'Community' }} />
      <ScrollView
        style={styles.container}
        contentContainerStyle={styles.contentContainer}
      >
        <View style={styles.header}>
          <Text variant="heading2">Community</Text>
          <Text variant="body" color="textSecondary" style={styles.subtitle}>
            Connect with others on similar healing journeys
          </Text>
        </View>

        <Card variant="elevated" style={styles.highlightCard}>
          <View style={styles.highlightContent}>
            <Feather name="users" size={32} style={styles.highlightIcon} />
            <Text variant="subtitle" style={styles.highlightTitle}>
              Join a Healing Circle
            </Text>
            <Text
              variant="body"
              color="textSecondary"
              style={styles.highlightDescription}
            >
              Small, guided groups focused on specific healing topics with
              weekly discussions and support.
            </Text>
            <Button
              title="Learn More"
              variant="primary"
              size="medium"
              style={styles.highlightButton}
              onPress={() => console.log('Learn about circles')}
            />
          </View>
        </Card>

        <Text variant="subtitle" style={styles.sectionTitle}>
          Available Communities
        </Text>

        {communities.map((community) => (
          <Card
            key={community.id}
            variant="elevated"
            style={styles.communityCard}
          >
            <View style={styles.communityHeader}>
              <Image
                source={{ uri: community.imageUrl }}
                style={styles.communityImage}
              />
            </View>
            <View style={styles.communityContent}>
              <Text variant="subtitle">{community.name}</Text>
              <Text
                variant="caption"
                color="primary"
                style={styles.memberCount}
              >
                {community.members} members
              </Text>
              <Text
                variant="body"
                color="textSecondary"
                style={styles.communityDescription}
              >
                {community.description}
              </Text>
              <View style={styles.communityActions}>
                <Button
                  title="Join"
                  variant="primary"
                  size="small"
                  style={styles.joinButton}
                  onPress={() => console.log(`Join community: ${community.id}`)}
                />
                <Button
                  title="Learn More"
                  variant="outline"
                  size="small"
                  onPress={() => console.log(`View community: ${community.id}`)}
                />
              </View>
            </View>
          </Card>
        ))}

        <Card variant="elevated" style={styles.createCard}>
          <View style={styles.createContent}>
            <Feather name="plus-circle" size={32} style={styles.createIcon} />
            <Text variant="subtitle" style={styles.createTitle}>
              Start Your Own Circle
            </Text>
            <Text
              variant="body"
              color="textSecondary"
              style={styles.createDescription}
            >
              Create a healing circle focused on a specific topic or challenge
              you're passionate about.
            </Text>
            <Button
              title="Create Circle"
              variant="primary"
              size="medium"
              style={styles.createButton}
              onPress={() => console.log('Create circle')}
            />
          </View>
        </Card>

        <View style={styles.footer}>
          <Button
            title="Return to Home"
            variant="outline"
            size="medium"
            onPress={() => router.back()}
          />
        </View>
      </ScrollView>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  contentContainer: {
    padding: Theme.spacing.m,
    paddingBottom: Theme.spacing.xl * 2,
  },
  header: {
    marginBottom: Theme.spacing.l,
  },
  subtitle: {
    marginTop: Theme.spacing.xs,
  },
  highlightCard: {
    marginBottom: Theme.spacing.l,
  },
  highlightContent: {
    padding: Theme.spacing.m,
    alignItems: 'center',
  },
  highlightIcon: {
    marginBottom: Theme.spacing.m,
  },
  highlightTitle: {
    marginBottom: Theme.spacing.s,
    textAlign: 'center',
  },
  highlightDescription: {
    textAlign: 'center',
    marginBottom: Theme.spacing.m,
  },
  highlightButton: {
    minWidth: 150,
  },
  sectionTitle: {
    marginBottom: Theme.spacing.m,
  },
  communityCard: {
    marginBottom: Theme.spacing.m,
    overflow: 'hidden',
  },
  communityHeader: {
    width: '100%',
    height: 120,
  },
  communityImage: {
    width: '100%',
    height: '100%',
  },
  communityContent: {
    padding: Theme.spacing.m,
  },
  memberCount: {
    marginTop: Theme.spacing.xs,
    marginBottom: Theme.spacing.s,
  },
  communityDescription: {
    marginBottom: Theme.spacing.m,
  },
  communityActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  joinButton: {
    marginRight: Theme.spacing.m,
  },
  createCard: {
    marginTop: Theme.spacing.l,
    marginBottom: Theme.spacing.l,
  },
  createContent: {
    padding: Theme.spacing.m,
    alignItems: 'center',
  },
  createIcon: {
    marginBottom: Theme.spacing.m,
  },
  createTitle: {
    marginBottom: Theme.spacing.s,
    textAlign: 'center',
  },
  createDescription: {
    textAlign: 'center',
    marginBottom: Theme.spacing.m,
  },
  createButton: {
    minWidth: 150,
  },
  footer: {
    marginTop: Theme.spacing.l,
    alignItems: 'center',
  },
});
