import { Stack, useRouter } from 'expo-router';
import React, { useState } from 'react';
import { Image, StyleSheet, TouchableOpacity } from 'react-native';
import { ScrollView } from 'react-native-gesture-handler';

import { Button } from '../components/ui/Button';
import { Card } from '../components/ui/Card';
import { Text } from '../components/ui/Text';
import { View } from '../components/ui/View';
import { colors } from '../constants/Colors';
import Theme from '../constants/Theme';

export default function KnowledgeScreen() {
  const router = useRouter();
  const [activeCategory, setActiveCategory] = useState('all');

  const categories = [
    { id: 'all', label: 'All' },
    { id: 'articles', label: 'Articles' },
    { id: 'videos', label: 'Videos' },
    { id: 'audio', label: 'Audio' },
  ];

  const resources = [
    {
      id: '1',
      title: 'Understanding Waswas: Overcoming Intrusive Thoughts',
      type: 'article',
      imageUrl:
        'https://images.pexels.com/photos/1261728/pexels-photo-1261728.jpeg',
      duration: '5 min read',
    },
    {
      id: '2',
      title: 'Healing the Heart: Qalb Purification',
      type: 'video',
      imageUrl:
        'https://images.pexels.com/photos/1261728/pexels-photo-1261728.jpeg',
      duration: '12 min watch',
    },
    {
      id: '3',
      title: 'Morning Dhikr Routine for Inner Peace',
      type: 'audio',
      imageUrl:
        'https://images.pexels.com/photos/1261728/pexels-photo-1261728.jpeg',
      duration: '7 min listen',
    },
  ];

  const filteredResources =
    activeCategory === 'all'
      ? resources
      : resources.filter(
          (resource) => resource.type === activeCategory.slice(0, -1)
        );

  return (
    <>
      <Stack.Screen options={{ title: 'Knowledge Hub' }} />
      <ScrollView
        style={styles.container}
        contentContainerStyle={styles.contentContainer}
      >
        <View style={styles.header}>
          <Text variant="heading2">Knowledge Hub</Text>
          <Text variant="body" color="textSecondary" style={styles.subtitle}>
            Islamic wisdom for healing body, mind, heart, and soul
          </Text>
        </View>

        <View style={styles.categoriesContainer}>
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            style={styles.categoriesScroll}
          >
            {categories.map((category) => (
              <TouchableOpacity
                key={category.id}
                style={[
                  styles.categoryTab,
                  activeCategory === category.id && {
                    backgroundColor: colors.primary,
                    borderColor: colors.primary,
                  },
                ]}
                onPress={() => setActiveCategory(category.id)}
              >
                <Text variant="caption">{category.label}</Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>

        <View style={styles.resourcesContainer}>
          {filteredResources.map((resource) => (
            <Card
              key={resource.id}
              variant="elevated"
              style={styles.resourceCard}
            >
              <TouchableOpacity
                onPress={() => console.log(`View resource: ${resource.id}`)}
              >
                <Image
                  source={{ uri: resource.imageUrl }}
                  style={styles.resourceImage}
                />
                <View style={styles.resourceContent}>
                  <Text
                    variant="caption"
                    color="textSecondary"
                    style={styles.resourceType}
                  >
                    {resource.type.toUpperCase()}
                  </Text>
                  <Text
                    variant="subtitle"
                    numberOfLines={2}
                    style={styles.resourceTitle}
                  >
                    {resource.title}
                  </Text>
                  <Text variant="caption" color="textSecondary">
                    {resource.duration}
                  </Text>
                </View>
              </TouchableOpacity>
            </Card>
          ))}
        </View>

        <Card variant="elevated" style={styles.featuredCard}>
          <View style={styles.featuredContent}>
            <Text variant="subtitle" style={styles.featuredTitle}>
              5-Layer Framework for Islamic Healing
            </Text>
            <Text
              variant="body"
              color="textSecondary"
              style={styles.featuredDescription}
            >
              Learn about the Islamic approach to holistic healing through the
              layers of Jism, Nafs, Aql, Qalb, and Ruh.
            </Text>
            <Button
              title="Explore Framework"
              variant="primary"
              size="medium"
              style={styles.featuredButton}
              onPress={() => console.log('Explore framework')}
            />
          </View>
        </Card>

        <View style={styles.footer}>
          <Button
            title="Return to Home"
            variant="outline"
            size="medium"
            onPress={() => router.back()}
          />
        </View>
      </ScrollView>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  contentContainer: {
    padding: Theme.spacing.m,
    paddingBottom: Theme.spacing.xl * 2,
  },
  header: {
    marginBottom: Theme.spacing.l,
  },
  subtitle: {
    marginTop: Theme.spacing.xs,
  },
  categoriesContainer: {
    marginBottom: Theme.spacing.m,
  },
  categoriesScroll: {
    marginLeft: -Theme.spacing.xs,
  },
  categoryTab: {
    paddingHorizontal: Theme.spacing.m,
    paddingVertical: Theme.spacing.s,
    marginRight: Theme.spacing.s,
    borderRadius: Theme.borderRadius.pill,
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  resourcesContainer: {
    marginBottom: Theme.spacing.l,
  },
  resourceCard: {
    marginBottom: Theme.spacing.m,
    overflow: 'hidden',
  },
  resourceImage: {
    width: '100%',
    height: 180,
  },
  resourceContent: {
    padding: Theme.spacing.m,
  },
  resourceType: {
    marginBottom: Theme.spacing.xs,
  },
  resourceTitle: {
    marginBottom: Theme.spacing.s,
  },
  featuredCard: {
    marginBottom: Theme.spacing.l,
  },
  featuredContent: {
    padding: Theme.spacing.m,
    alignItems: 'center',
  },
  featuredTitle: {
    marginBottom: Theme.spacing.s,
    textAlign: 'center',
  },
  featuredDescription: {
    textAlign: 'center',
    marginBottom: Theme.spacing.m,
  },
  featuredButton: {
    minWidth: 200,
  },
  footer: {
    marginTop: Theme.spacing.l,
    alignItems: 'center',
  },
});
