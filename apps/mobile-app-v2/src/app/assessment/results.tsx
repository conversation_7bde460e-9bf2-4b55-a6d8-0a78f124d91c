/**
 * Diagnosis Results Screen for Feature 1: Understanding Your Inner Landscape (REVISED)
 * Displays personalized spiritual diagnosis with Islamic layer analysis
 */

import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
  Alert,
  ActivityIndicator,
  Animated,
  Share,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { router, useLocalSearchParams } from 'expo-router';

import { assessmentService } from '../../services/assessment.service';
import { colors } from '../../constants/Colors';

interface SpiritualDiagnosis {
  id: string;
  userId: string;
  assessmentId: string;
  primaryLayer: LayerAnalysis;
  secondaryLayers: LayerAnalysis[];
  overallSeverity: 'mild' | 'moderate' | 'severe' | 'critical';
  personalizedMessage: string;
  islamicInsights: string[];
  educationalContent: string;
  crisisLevel: 'none' | 'low' | 'moderate' | 'high' | 'critical';
  crisisIndicators: string[];
  immediateActions: string[];
  nextSteps: string[];
  recommendedJourneyType: string;
  estimatedHealingDuration: number;
  confidence: number;
  generatedAt: Date;
}

interface LayerAnalysis {
  layer: 'jism' | 'nafs' | 'aql' | 'qalb' | 'ruh';
  layerName: string;
  impactScore: number;
  affectedSymptoms: string[];
  insights: string[];
  recommendations: string[];
  islamicContext: string;
  priority: 'primary' | 'secondary' | 'tertiary';
}

interface DiagnosisDelivery {
  userId: string;
  userType: string;
  diagnosis: SpiritualDiagnosis;
  deliveryStyle: 'gentle' | 'clinical' | 'advanced' | 'traditional';
  layerIntroduction: string;
  primaryLayerAnalysis: string;
  secondaryLayerConnections: string;
  islamicInsights: string;
  practicalImplications: string;
  layerEducation: {
    jism: string;
    nafs: string;
    aql: string;
    qalb: string;
    ruh: string;
  };
  nextStepsGuidance: string;
  journeyRecommendation: string;
  additionalResources: string[];
}

export default function DiagnosisResultsScreen() {
  const { sessionId } = useLocalSearchParams();

  const [diagnosis, setDiagnosis] = useState<SpiritualDiagnosis | null>(null);
  const [delivery, setDelivery] = useState<DiagnosisDelivery | null>(null);
  const [loading, setLoading] = useState(true);
  const [showFeedback, setShowFeedback] = useState(false);
  const [expandedSections, setExpandedSections] = useState<Set<string>>(
    new Set()
  );

  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(50)).current;

  useEffect(() => {
    loadDiagnosis();
  }, []);

  useEffect(() => {
    if (diagnosis && delivery) {
      // Animate entrance
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 800,
          useNativeDriver: true,
        }),
        Animated.timing(slideAnim, {
          toValue: 0,
          duration: 600,
          useNativeDriver: true,
        }),
      ]).start();
    }
  }, [diagnosis, delivery]);

  const loadDiagnosis = async () => {
    try {
      setLoading(true);
      const result = await assessmentService.getDiagnosis(sessionId as string);
      setDiagnosis(result.diagnosis);
      setDelivery(result.delivery);
    } catch (err) {
      console.error('Error loading diagnosis:', err);
      Alert.alert('Error', 'Failed to load your diagnosis. Please try again.', [
        { text: 'Retry', onPress: loadDiagnosis },
        { text: 'Go Back', onPress: () => router.back() },
      ]);
    } finally {
      setLoading(false);
    }
  };

  const toggleSection = (sectionId: string) => {
    setExpandedSections((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(sectionId)) {
        newSet.delete(sectionId);
      } else {
        newSet.add(sectionId);
      }
      return newSet;
    });
  };

  const handleShare = async () => {
    if (!diagnosis || !delivery) return;

    try {
      const shareContent = `
My Spiritual Diagnosis from Qalb Healing:

Primary Layer: ${diagnosis.primaryLayer.layerName}
Overall Assessment: ${diagnosis.overallSeverity}

Key Insights:
${diagnosis.islamicInsights.slice(0, 2).join('\n')}

Recommended Journey: ${diagnosis.recommendedJourneyType}

Download Qalb Healing to start your own spiritual assessment.
      `.trim();

      await Share.share({
        message: shareContent,
        title: 'My Spiritual Diagnosis',
      });
    } catch (err) {
      console.error('Error sharing:', err);
    }
  };

  const handleStartJourney = () => {
    router.push({
      pathname: '/journeys',
      params: {
        recommendedJourney: diagnosis?.recommendedJourneyType,
        diagnosisId: diagnosis?.id,
      },
    });
  };

  const handleFeedbackSubmit = async (feedback: any) => {
    try {
      await assessmentService.submitDiagnosisFeedback(diagnosis!.id, feedback);
      setShowFeedback(false);
      Alert.alert(
        'Thank You',
        'Your feedback helps us improve our assessments for everyone.',
        [{ text: 'OK' }]
      );
    } catch (err) {
      console.error('Error submitting feedback:', err);
      Alert.alert('Error', 'Failed to submit feedback. Please try again.');
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'mild':
        return '#4CAF50';
      case 'moderate':
        return '#FF9800';
      case 'severe':
        return '#F44336';
      case 'critical':
        return '#D32F2F';
      default:
        return colors.primary;
    }
  };

  const getSeverityIcon = (severity: string) => {
    switch (severity) {
      case 'mild':
        return 'checkmark-circle';
      case 'moderate':
        return 'warning';
      case 'severe':
        return 'alert-circle';
      case 'critical':
        return 'alert';
      default:
        return 'information-circle';
    }
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={colors.primary} />
        <Text style={styles.loadingText}>
          Generating your spiritual diagnosis...
        </Text>
        <Text style={styles.loadingSubtext}>
          "And Allah is the best of healers" - Quran 26:80
        </Text>
      </View>
    );
  }

  if (!diagnosis || !delivery) {
    return (
      <View style={styles.errorContainer}>
        <Ionicons name="alert-circle-outline" size={48} color={colors.error} />
        <Text style={styles.errorText}>Unable to load your diagnosis</Text>
        <TouchableOpacity style={styles.retryButton} onPress={loadDiagnosis}>
          <Text style={styles.retryButtonText}>Try Again</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <LinearGradient
        colors={['#E8F5E8', '#F0F8F0', '#FFFFFF']}
        style={styles.gradient}
      >
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => router.back()}
          >
            <Ionicons name="arrow-back" size={24} color={colors.text} />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Your Spiritual Diagnosis</Text>
          <TouchableOpacity style={styles.shareButton} onPress={handleShare}>
            <Ionicons name="share-outline" size={24} color={colors.primary} />
          </TouchableOpacity>
        </View>

        <Animated.View
          style={[
            styles.content,
            {
              opacity: fadeAnim,
              transform: [{ translateY: slideAnim }],
            },
          ]}
        >
          <ScrollView
            style={styles.scrollView}
            showsVerticalScrollIndicator={false}
          >
            {/* Introduction */}
            <View style={styles.introContainer}>
              <Text style={styles.introText}>{delivery.layerIntroduction}</Text>
            </View>

            {/* Primary Layer Analysis */}
            <View style={styles.sectionContainer}>
              <TouchableOpacity
                style={styles.sectionHeader}
                onPress={() => toggleSection('primary')}
              >
                <View style={styles.sectionTitleContainer}>
                  <View
                    style={[
                      styles.severityIndicator,
                      {
                        backgroundColor: getSeverityColor(
                          diagnosis.overallSeverity
                        ),
                      },
                    ]}
                  >
                    <Ionicons
                      name={getSeverityIcon(diagnosis.overallSeverity)}
                      size={16}
                      color="white"
                    />
                  </View>
                  <Text style={styles.sectionTitle}>Primary Focus</Text>
                </View>
                <Ionicons
                  name={
                    expandedSections.has('primary')
                      ? 'chevron-up'
                      : 'chevron-down'
                  }
                  size={20}
                  color={colors.textSecondary}
                />
              </TouchableOpacity>

              {expandedSections.has('primary') && (
                <View style={styles.layerCard}>
                  <Text style={styles.layerTitle}>
                    {diagnosis.primaryLayer.layerName}
                  </Text>
                  <Text style={styles.layerDescription}>
                    {diagnosis.primaryLayer.islamicContext}
                  </Text>
                  <View style={styles.layerInsights}>
                    {diagnosis.primaryLayer.insights.map((insight, index) => (
                      <Text key={index} style={styles.insightText}>
                        • {insight}
                      </Text>
                    ))}
                  </View>
                </View>
              )}
            </View>

            {/* Islamic Insights */}
            <View style={styles.sectionContainer}>
              <TouchableOpacity
                style={styles.sectionHeader}
                onPress={() => toggleSection('insights')}
              >
                <Text style={styles.sectionTitle}>Islamic Insights</Text>
                <Ionicons
                  name={
                    expandedSections.has('insights')
                      ? 'chevron-up'
                      : 'chevron-down'
                  }
                  size={20}
                  color={colors.textSecondary}
                />
              </TouchableOpacity>

              {expandedSections.has('insights') && (
                <View style={styles.insightsContainer}>
                  {diagnosis.islamicInsights.map((insight, index) => (
                    <View key={index} style={styles.insightCard}>
                      <Text style={styles.insightText}>{insight}</Text>
                    </View>
                  ))}
                </View>
              )}
            </View>

            {/* Next Steps */}
            <View style={styles.sectionContainer}>
              <TouchableOpacity
                style={styles.sectionHeader}
                onPress={() => toggleSection('nextsteps')}
              >
                <Text style={styles.sectionTitle}>Your Next Steps</Text>
                <Ionicons
                  name={
                    expandedSections.has('nextsteps')
                      ? 'chevron-up'
                      : 'chevron-down'
                  }
                  size={20}
                  color={colors.textSecondary}
                />
              </TouchableOpacity>

              {expandedSections.has('nextsteps') && (
                <View style={styles.nextStepsContainer}>
                  {diagnosis.nextSteps.map((step, index) => (
                    <View key={index} style={styles.nextStepItem}>
                      <View style={styles.stepNumber}>
                        <Text style={styles.stepNumberText}>{index + 1}</Text>
                      </View>
                      <Text style={styles.stepText}>{step}</Text>
                    </View>
                  ))}
                </View>
              )}
            </View>

            {/* Journey Recommendation */}
            <View style={styles.journeyContainer}>
              <Text style={styles.journeyTitle}>
                Recommended Healing Journey
              </Text>
              <Text style={styles.journeyName}>
                {diagnosis.recommendedJourneyType}
              </Text>
              <Text style={styles.journeyDescription}>
                Estimated duration: {diagnosis.estimatedHealingDuration} days
              </Text>
              <Text style={styles.journeyDescription}>
                Confidence: {Math.round(diagnosis.confidence * 100)}%
              </Text>
            </View>
          </ScrollView>

          {/* Action Buttons */}
          <View style={styles.actionsContainer}>
            <TouchableOpacity
              style={styles.primaryButton}
              onPress={handleStartJourney}
            >
              <LinearGradient
                colors={[colors.primary, colors.primaryLight]}
                style={styles.primaryButtonGradient}
              >
                <Text style={styles.primaryButtonText}>
                  Start Your Healing Journey
                </Text>
                <Ionicons name="arrow-forward" size={20} color="white" />
              </LinearGradient>
            </TouchableOpacity>

            <View style={styles.secondaryActions}>
              <TouchableOpacity
                style={styles.secondaryButton}
                onPress={() => setShowFeedback(true)}
              >
                <Text style={styles.secondaryButtonText}>
                  Rate This Diagnosis
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.secondaryButton}
                onPress={() => router.push('/(tabs)')}
              >
                <Text style={styles.secondaryButtonText}>Go to Dashboard</Text>
              </TouchableOpacity>
            </View>
          </View>
        </Animated.View>

        {/* Feedback Modal */}
        {showFeedback && (
          <View style={styles.feedbackModal}>
            <View style={styles.feedbackModalContent}>
              <Text style={styles.feedbackModalTitle}>Rate Your Diagnosis</Text>
              <Text style={styles.feedbackModalText}>
                How accurate and helpful was your spiritual diagnosis?
              </Text>
              <View style={styles.feedbackActions}>
                <TouchableOpacity
                  style={styles.feedbackButton}
                  onPress={() =>
                    handleFeedbackSubmit({ accuracy: 5, helpfulness: 5 })
                  }
                >
                  <Text style={styles.feedbackButtonText}>Excellent</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={styles.feedbackButton}
                  onPress={() =>
                    handleFeedbackSubmit({ accuracy: 3, helpfulness: 3 })
                  }
                >
                  <Text style={styles.feedbackButtonText}>Good</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={styles.feedbackButton}
                  onPress={() => setShowFeedback(false)}
                >
                  <Text style={styles.feedbackButtonText}>Skip</Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        )}
      </LinearGradient>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  gradient: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  loadingText: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text,
    marginTop: 16,
    textAlign: 'center',
  },
  loadingSubtext: {
    fontSize: 16,
    color: colors.textSecondary,
    marginTop: 8,
    textAlign: 'center',
    fontStyle: 'italic',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  errorText: {
    fontSize: 16,
    color: colors.error,
    textAlign: 'center',
    marginVertical: 16,
  },
  retryButton: {
    backgroundColor: colors.primary,
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  retryButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: 'white',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 24,
    paddingTop: 16,
  },
  backButton: {
    marginRight: 16,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: colors.text,
    flex: 1,
  },
  shareButton: {
    marginLeft: 16,
  },
  content: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
    padding: 24,
  },
  introContainer: {
    backgroundColor: 'white',
    padding: 24,
    borderRadius: 12,
    marginBottom: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  introText: {
    fontSize: 16,
    color: colors.text,
    lineHeight: 24,
  },
  sectionContainer: {
    backgroundColor: 'white',
    borderRadius: 12,
    marginBottom: 24,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 24,
    backgroundColor: colors.surface,
  },
  sectionTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  severityIndicator: {
    width: 24,
    height: 24,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text,
  },
  layerCard: {
    padding: 24,
  },
  layerTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: colors.primary,
    marginBottom: 8,
  },
  layerDescription: {
    fontSize: 16,
    color: colors.textSecondary,
    lineHeight: 22,
    marginBottom: 16,
  },
  layerInsights: {
    gap: 8,
  },
  insightText: {
    fontSize: 16,
    color: colors.text,
    lineHeight: 22,
  },
  insightsContainer: {
    padding: 24,
    gap: 16,
  },
  insightCard: {
    backgroundColor: colors.surface,
    padding: 16,
    borderRadius: 8,
  },
  nextStepsContainer: {
    padding: 24,
    gap: 16,
  },
  nextStepItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  stepNumber: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: colors.primary,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16,
  },
  stepNumberText: {
    fontSize: 14,
    fontWeight: '600',
    color: 'white',
  },
  stepText: {
    fontSize: 16,
    color: colors.text,
    lineHeight: 22,
    flex: 1,
  },
  journeyContainer: {
    backgroundColor: 'white',
    padding: 24,
    borderRadius: 12,
    marginBottom: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  journeyTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text,
    marginBottom: 8,
  },
  journeyName: {
    fontSize: 20,
    fontWeight: '700',
    color: colors.primary,
    marginBottom: 8,
  },
  journeyDescription: {
    fontSize: 16,
    color: colors.textSecondary,
    marginBottom: 4,
  },
  actionsContainer: {
    padding: 24,
    gap: 16,
  },
  primaryButton: {
    borderRadius: 12,
    overflow: 'hidden',
  },
  primaryButtonGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 24,
    gap: 8,
  },
  primaryButtonText: {
    fontSize: 18,
    fontWeight: '600',
    color: 'white',
  },
  secondaryActions: {
    flexDirection: 'row',
    gap: 16,
  },
  secondaryButton: {
    flex: 1,
    backgroundColor: 'white',
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: colors.border,
    alignItems: 'center',
  },
  secondaryButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.primary,
  },
  feedbackModal: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  feedbackModalContent: {
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 24,
    width: '100%',
    maxWidth: 400,
  },
  feedbackModalTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: colors.text,
    marginBottom: 16,
    textAlign: 'center',
  },
  feedbackModalText: {
    fontSize: 16,
    color: colors.textSecondary,
    lineHeight: 22,
    marginBottom: 24,
    textAlign: 'center',
  },
  feedbackActions: {
    gap: 12,
  },
  feedbackButton: {
    backgroundColor: colors.surface,
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
  },
  feedbackButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text,
  },
});
