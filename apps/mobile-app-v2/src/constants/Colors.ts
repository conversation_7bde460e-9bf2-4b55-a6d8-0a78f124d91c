/**
 * Color definitions for Qalb Healing app
 * Includes both light and dark theme colors and Islamic-themed colors for the soul layers
 */

// Define base colors
const tintColorLight = '#2f95dc';
const tintColorDark = '#fff';

// Define theme color types with TypeScript
export type ThemeColorScheme = 'light' | 'dark';

// Interface defining required properties for each theme
export interface ColorTheme {
  // Base UI colors
  text: string;
  textSecondary: string;
  textDisabled: string;
  background: string;
  cardBackground: string;
  tint: string;
  tabIconDefault: string;
  tabIconSelected: string;
  primary: string;
  primaryLight: string; // Light variation of primary color
  secondary: string;
  success: string;
  warning: string;
  error: string;
  info: string;
  surface: string;
  border: string;
  disabled: string;
  emergencyRed: string;

  // Islamic Soul Layer Colors
  jismRed: string; // Body layer color
  nafsOrange: string; // Emotions layer color
  aqlYellow: string; // Mind layer color
  qalbGreen: string; // Heart layer color
  spiritualBlue: string; // Soul layer color
  ruhGold: string; // Additional spiritual accent

  // Allow for additional theme-specific colors
  [key: string]: string;
}

// Type for the full color theme object
export interface ColorThemes {
  light: ColorTheme;
  dark: ColorTheme;
}

// Define and export the colors object
const Colors: ColorThemes = {
  light: {
    text: '#212121',
    textSecondary: '#757575',
    textDisabled: '#BDBDBD',
    background: '#F4F6F8',
    cardBackground: '#fff',
    tint: tintColorLight,
    tabIconDefault: '#ccc',
    tabIconSelected: tintColorLight,
    primary: '#2E7D32', // Dark Green - Islamic theme
    primaryLight: '#C8E6C9', // Light Green
    secondary: '#6A1B9A', // Deep Purple
    success: '#4CAF50',
    warning: '#FF9800',
    error: '#F44336',
    info: '#2196F3',
    surface: '#fff',
    border: '#E0E0E0',
    disabled: '#cccccc',
    emergencyRed: '#DC2626',

    // Soul layer specific colors - used throughout the app for consistent color coding
    jismRed: '#D32F2F', // Body (physical) layer
    nafsOrange: '#F57C00', // Emotions layer
    aqlYellow: '#FBC02D', // Mind (thoughts) layer
    qalbGreen: '#388E3C', // Heart (spiritual heart) layer
    spiritualBlue: '#1976D2', // Soul/connection layer
    ruhGold: '#FFD700', // Additional spiritual accent
  },
  dark: {
    text: '#E0E0E0',
    textSecondary: '#B0B0B0',
    textDisabled: '#666666',
    background: '#121212',
    cardBackground: '#1E1E1E',
    tint: tintColorDark,
    tabIconDefault: '#666',
    tabIconSelected: tintColorDark,
    primary: '#81C784', // Light Green - more visible in dark mode
    primaryLight: '#A5D6A7', // Lighter Green
    secondary: '#BA68C8', // Purple
    success: '#66BB6A',
    warning: '#FFA726',
    error: '#EF5350',
    info: '#64B5F6',
    surface: '#1e1e1e',
    border: '#333333',
    disabled: '#666666',
    emergencyRed: '#EF4444',

    // Soul layer specific colors - dark theme versions
    jismRed: '#EF5350', // Body (physical) layer
    nafsOrange: '#FFB74D', // Emotions layer
    aqlYellow: '#FFF176', // Mind (thoughts) layer
    qalbGreen: '#81C784', // Heart (spiritual heart) layer
    spiritualBlue: '#64B5F6', // Soul/connection layer
    ruhGold: '#FFD54F', // Additional spiritual accent
  },
};

// Export the full theme object as default
export default Colors;

// Export the full theme object for theme switching
export const ColorThemes = Colors;

// Export a default theme colors for direct access (defaulting to light theme)
// This allows components to use Colors.primary instead of Colors.light.primary
export const colors = Colors.light;
