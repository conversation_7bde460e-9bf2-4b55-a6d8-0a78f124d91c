/**
 * Crisis Modal Component for Feature 0: Adaptive Onboarding
 * Emergency intervention modal for crisis detection
 */

import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  TouchableOpacity,
  ScrollView,
  Linking,
  Alert,
  Dimensions,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { BlurView } from 'expo-blur';

interface CrisisAction {
  id: string;
  text: string;
  primary?: boolean;
  urgent?: boolean;
  emergency?: boolean;
  phone?: string;
  url?: string;
}

interface CrisisData {
  level: 'low' | 'moderate' | 'high' | 'critical';
  message: string;
  actions: CrisisAction[];
  urgency: 'low' | 'moderate' | 'urgent' | 'immediate';
  indicators?: string[];
}

interface CrisisModalProps {
  visible: boolean;
  crisisData: CrisisData | null;
  onAction: (actionId: string) => void;
  onClose: () => void;
}

const { width, height } = Dimensions.get('window');

export const CrisisModal: React.FC<CrisisModalProps> = ({
  visible,
  crisisData,
  onAction,
  onClose,
}) => {
  if (!crisisData) return null;

  const handleAction = async (action: CrisisAction) => {
    try {
      if (action.phone) {
        // Open phone dialer
        const phoneUrl = `tel:${action.phone}`;
        const canOpen = await Linking.canOpenURL(phoneUrl);
        if (canOpen) {
          await Linking.openURL(phoneUrl);
        } else {
          Alert.alert('Error', 'Unable to open phone dialer');
        }
      } else if (action.url) {
        // Open URL
        const canOpen = await Linking.canOpenURL(action.url);
        if (canOpen) {
          await Linking.openURL(action.url);
        } else {
          Alert.alert('Error', 'Unable to open link');
        }
      } else {
        // Handle app action
        onAction(action.id);
      }
    } catch (error) {
      console.error('Failed to handle crisis action:', error);
      Alert.alert('Error', 'Unable to complete action');
    }
  };

  const getModalStyle = () => {
    switch (crisisData.level) {
      case 'critical':
        return {
          backgroundColor: '#dc2626',
          borderColor: '#fca5a5',
        };
      case 'high':
        return {
          backgroundColor: '#ea580c',
          borderColor: '#fdba74',
        };
      case 'moderate':
        return {
          backgroundColor: '#d97706',
          borderColor: '#fcd34d',
        };
      default:
        return {
          backgroundColor: '#1f2937',
          borderColor: '#6b7280',
        };
    }
  };

  const getIconName = () => {
    switch (crisisData.level) {
      case 'critical':
        return 'warning';
      case 'high':
        return 'alert-circle';
      case 'moderate':
        return 'information-circle';
      default:
        return 'help-circle';
    }
  };

  const modalStyle = getModalStyle();

  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      statusBarTranslucent
    >
      <BlurView intensity={50} style={styles.overlay}>
        <View style={styles.modalContainer}>
          <View style={[styles.modal, { borderColor: modalStyle.borderColor }]}>
            <LinearGradient
              colors={[
                modalStyle.backgroundColor,
                `${modalStyle.backgroundColor}dd`,
              ]}
              style={styles.modalGradient}
            >
              {/* Header */}
              <View style={styles.header}>
                <View style={styles.iconContainer}>
                  <Ionicons
                    name={getIconName() as any}
                    size={32}
                    color="#ffffff"
                  />
                </View>

                <Text style={styles.title}>
                  {crisisData.level === 'critical'
                    ? 'Immediate Support Needed'
                    : crisisData.level === 'high'
                    ? 'Crisis Support Available'
                    : crisisData.level === 'moderate'
                    ? 'Additional Support'
                    : 'Support Resources'}
                </Text>

                {crisisData.level !== 'critical' && (
                  <TouchableOpacity
                    style={styles.closeButton}
                    onPress={onClose}
                  >
                    <Ionicons name="close" size={24} color="#ffffff" />
                  </TouchableOpacity>
                )}
              </View>

              {/* Content */}
              <ScrollView
                style={styles.content}
                showsVerticalScrollIndicator={false}
              >
                {/* Islamic Greeting */}
                <View style={styles.greetingContainer}>
                  <Text style={styles.arabicText}>
                    بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ
                  </Text>
                  <Text style={styles.translationText}>
                    In the name of Allah, the Most Gracious, the Most Merciful
                  </Text>
                </View>

                {/* Message */}
                <Text style={styles.message}>{crisisData.message}</Text>

                {/* Quranic Verse for Comfort */}
                {crisisData.level === 'critical' && (
                  <View style={styles.verseContainer}>
                    <Text style={styles.verseArabic}>
                      وَمَن يَتَوَكَّلْ عَلَى اللَّهِ فَهُوَ حَسْبُهُ ۚ إِنَّ
                      اللَّهَ بَالِغُ أَمْرِهِ
                    </Text>
                    <Text style={styles.verseTranslation}>
                      "And whoever relies upon Allah - then He is sufficient for
                      him. Indeed, Allah will accomplish His purpose." (Quran
                      65:3)
                    </Text>
                  </View>
                )}

                {/* Emergency Actions */}
                <View style={styles.actionsContainer}>
                  {crisisData.actions.map((action, index) => (
                    <TouchableOpacity
                      key={action.id}
                      style={[
                        styles.actionButton,
                        action.emergency && styles.emergencyButton,
                        action.primary && styles.primaryButton,
                        { marginTop: index > 0 ? 12 : 0 },
                      ]}
                      onPress={() => handleAction(action)}
                    >
                      <LinearGradient
                        colors={
                          action.emergency
                            ? ['#dc2626', '#b91c1c']
                            : action.primary
                            ? ['#059669', '#047857']
                            : [
                                'rgba(255, 255, 255, 0.2)',
                                'rgba(255, 255, 255, 0.1)',
                              ]
                        }
                        style={styles.actionGradient}
                      >
                        <View style={styles.actionContent}>
                          {action.emergency && (
                            <Ionicons
                              name="call"
                              size={20}
                              color="#ffffff"
                              style={styles.actionIcon}
                            />
                          )}
                          {action.id === 'emergency_sakina' && (
                            <Ionicons
                              name="heart"
                              size={20}
                              color="#ffffff"
                              style={styles.actionIcon}
                            />
                          )}
                          {action.id === 'crisis_counselor' && (
                            <Ionicons
                              name="people"
                              size={20}
                              color="#ffffff"
                              style={styles.actionIcon}
                            />
                          )}

                          <Text
                            style={[
                              styles.actionText,
                              action.emergency && styles.emergencyText,
                            ]}
                          >
                            {action.text}
                          </Text>

                          {action.urgent && (
                            <View style={styles.urgentBadge}>
                              <Text style={styles.urgentText}>URGENT</Text>
                            </View>
                          )}
                        </View>
                      </LinearGradient>
                    </TouchableOpacity>
                  ))}
                </View>

                {/* Crisis Resources */}
                <View style={styles.resourcesContainer}>
                  <Text style={styles.resourcesTitle}>
                    24/7 Crisis Resources
                  </Text>

                  <TouchableOpacity
                    style={styles.resourceButton}
                    onPress={() =>
                      handleAction({
                        id: 'suicide_prevention',
                        text: 'National Suicide Prevention Lifeline',
                        phone: '988',
                      })
                    }
                  >
                    <View style={styles.resourceContent}>
                      <Ionicons name="call" size={16} color="#ffffff" />
                      <Text style={styles.resourceText}>
                        National Suicide Prevention: 988
                      </Text>
                    </View>
                  </TouchableOpacity>

                  <TouchableOpacity
                    style={styles.resourceButton}
                    onPress={() =>
                      handleAction({
                        id: 'crisis_text',
                        text: 'Crisis Text Line',
                        phone: '741741',
                      })
                    }
                  >
                    <View style={styles.resourceContent}>
                      <Ionicons name="chatbubble" size={16} color="#ffffff" />
                      <Text style={styles.resourceText}>
                        Crisis Text Line: Text HOME to 741741
                      </Text>
                    </View>
                  </TouchableOpacity>
                </View>

                {/* Reminder */}
                <View style={styles.reminderContainer}>
                  <Text style={styles.reminderText}>
                    Remember: Allah is always with you. You are not alone in
                    this struggle. Your life has immense value and purpose.
                  </Text>
                </View>
              </ScrollView>
            </LinearGradient>
          </View>
        </View>
      </BlurView>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
  },
  modalContainer: {
    width: width * 0.9,
    maxHeight: height * 0.8,
  },
  modal: {
    borderRadius: 16,
    borderWidth: 2,
    overflow: 'hidden',
  },
  modalGradient: {
    flex: 1,
  },
  header: {
    padding: 20,
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255, 255, 255, 0.2)',
  },
  iconContainer: {
    width: 64,
    height: 64,
    borderRadius: 32,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
    color: '#ffffff',
    textAlign: 'center',
    fontFamily: 'Poppins-SemiBold',
  },
  closeButton: {
    position: 'absolute',
    top: 20,
    right: 20,
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    flex: 1,
    padding: 20,
  },
  greetingContainer: {
    alignItems: 'center',
    marginBottom: 20,
  },
  arabicText: {
    fontSize: 18,
    color: '#ffffff',
    fontWeight: '600',
    textAlign: 'center',
    marginBottom: 4,
  },
  translationText: {
    fontSize: 12,
    color: 'rgba(255, 255, 255, 0.8)',
    textAlign: 'center',
    fontStyle: 'italic',
  },
  message: {
    fontSize: 16,
    color: '#ffffff',
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 20,
    fontFamily: 'Poppins-Regular',
  },
  verseContainer: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 12,
    padding: 16,
    marginBottom: 20,
  },
  verseArabic: {
    fontSize: 16,
    color: '#ffffff',
    textAlign: 'center',
    marginBottom: 8,
    fontWeight: '500',
  },
  verseTranslation: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.9)',
    textAlign: 'center',
    lineHeight: 20,
    fontStyle: 'italic',
  },
  actionsContainer: {
    marginBottom: 20,
  },
  actionButton: {
    borderRadius: 12,
    overflow: 'hidden',
  },
  emergencyButton: {
    borderWidth: 2,
    borderColor: '#fca5a5',
  },
  primaryButton: {
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.3)',
  },
  actionGradient: {
    padding: 16,
  },
  actionContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  actionIcon: {
    marginRight: 8,
  },
  actionText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#ffffff',
    textAlign: 'center',
    fontFamily: 'Poppins-SemiBold',
  },
  emergencyText: {
    fontSize: 18,
  },
  urgentBadge: {
    backgroundColor: '#dc2626',
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 4,
    marginLeft: 8,
  },
  urgentText: {
    fontSize: 10,
    fontWeight: '700',
    color: '#ffffff',
  },
  resourcesContainer: {
    marginBottom: 20,
  },
  resourcesTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#ffffff',
    marginBottom: 12,
    textAlign: 'center',
    fontFamily: 'Poppins-SemiBold',
  },
  resourceButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 8,
    padding: 12,
    marginBottom: 8,
  },
  resourceContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  resourceText: {
    fontSize: 14,
    color: '#ffffff',
    marginLeft: 8,
    fontFamily: 'Poppins-Regular',
  },
  reminderContainer: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 12,
    padding: 16,
  },
  reminderText: {
    fontSize: 14,
    color: '#ffffff',
    textAlign: 'center',
    lineHeight: 20,
    fontFamily: 'Poppins-Regular',
  },
});

export default CrisisModal;
