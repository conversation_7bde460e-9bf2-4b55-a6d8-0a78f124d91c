/**
 * Community Card Component
 * Displays community engagement and activity information
 */

import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ViewStyle,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

interface CommunityCardProps {
  onPress: () => void;
  memberCount?: number;
  recentActivity?: string;
  unreadMessages?: number;
  style?: ViewStyle;
}

export function CommunityCard({
  onPress,
  memberCount = 0,
  recentActivity,
  unreadMessages = 0,
  style,
}: CommunityCardProps) {
  return (
    <TouchableOpacity
      style={[styles.container, style]}
      onPress={onPress}
      activeOpacity={0.8}
    >
      <View style={styles.content}>
        {/* Header */}
        <View style={styles.header}>
          <View style={styles.titleContainer}>
            <View style={styles.iconContainer}>
              <Ionicons name="people" size={24} color="#4a90a4" />
            </View>
            <View style={styles.titleInfo}>
              <Text style={styles.title}>Community Support</Text>
              <Text style={styles.subtitle}>
                {memberCount} members in your group
              </Text>
            </View>
          </View>

          {/* Notification Badge */}
          {unreadMessages > 0 && (
            <View style={styles.notificationBadge}>
              <Text style={styles.notificationText}>
                {unreadMessages > 99 ? '99+' : unreadMessages}
              </Text>
            </View>
          )}
        </View>

        {/* Recent Activity */}
        {recentActivity && (
          <View style={styles.activityContainer}>
            <View style={styles.activityIcon}>
              <Ionicons name="pulse" size={16} color="#38a169" />
            </View>
            <Text style={styles.activityText}>{recentActivity}</Text>
          </View>
        )}

        {/* Community Features */}
        <View style={styles.featuresContainer}>
          <View style={styles.feature}>
            <Ionicons name="heart" size={16} color="#e74c3c" />
            <Text style={styles.featureText}>Peer Support</Text>
          </View>
          
          <View style={styles.feature}>
            <Ionicons name="book" size={16} color="#9f7aea" />
            <Text style={styles.featureText}>Shared Reflections</Text>
          </View>
          
          <View style={styles.feature}>
            <Ionicons name="star" size={16} color="#ffd700" />
            <Text style={styles.featureText}>Group Activities</Text>
          </View>
        </View>

        {/* Action Footer */}
        <View style={styles.footer}>
          <Text style={styles.actionText}>Join Community Discussion</Text>
          <Ionicons
            name="chevron-forward"
            size={16}
            color="rgba(255, 255, 255, 0.6)"
          />
        </View>
      </View>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 16,
    padding: 16,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.1)',
  },
  content: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: 'rgba(74, 144, 164, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  titleInfo: {
    flex: 1,
  },
  title: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
    fontFamily: 'Poppins-SemiBold',
  },
  subtitle: {
    color: 'rgba(255, 255, 255, 0.7)',
    fontSize: 14,
    fontFamily: 'Poppins-Regular',
  },
  notificationBadge: {
    backgroundColor: '#e74c3c',
    borderRadius: 12,
    minWidth: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 8,
  },
  notificationText: {
    color: '#ffffff',
    fontSize: 12,
    fontWeight: '600',
    fontFamily: 'Poppins-SemiBold',
  },
  activityContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(56, 161, 105, 0.1)',
    borderRadius: 8,
    padding: 12,
    marginBottom: 16,
  },
  activityIcon: {
    marginRight: 8,
  },
  activityText: {
    color: '#38a169',
    fontSize: 14,
    fontWeight: '500',
    flex: 1,
    fontFamily: 'Poppins-Medium',
  },
  featuresContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  feature: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    justifyContent: 'center',
  },
  featureText: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: 12,
    marginLeft: 4,
    fontFamily: 'Poppins-Regular',
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: 'rgba(255, 255, 255, 0.1)',
  },
  actionText: {
    color: '#4a90a4',
    fontSize: 14,
    fontWeight: '500',
    fontFamily: 'Poppins-Medium',
  },
});
