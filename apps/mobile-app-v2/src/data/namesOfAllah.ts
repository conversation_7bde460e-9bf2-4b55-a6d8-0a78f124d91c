import { Name<PERSON><PERSON><PERSON><PERSON> } from '../types';

export const namesOfAllah: Name<PERSON><PERSON><PERSON><PERSON>[] = [
  {
    id: '1',
    name: 'الرَّحْمَٰنُ',
    arabic: 'الرَّحْمَٰنُ',
    transliteration: '<PERSON><PERSON><PERSON><PERSON>',
    translation: 'The Most Merciful',
    meaning:
      'The One who has plenty of mercy for the believers and the blasphemers in this world and especially for the believers in the hereafter.',
    benefits: [
      'Brings mercy and compassion',
      'Softens the heart',
      'Increases empathy',
    ],
    category: 'mercy',
    isSaved: false,
  },
  {
    id: '2',
    name: 'الرَّحِيمُ',
    arabic: 'الرَّحِيمُ',
    transliteration: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',
    translation: 'The Most Compassionate',
    meaning: 'The One who has plenty of mercy for the believers.',
    benefits: [
      'Increases compassion',
      'Brings inner peace',
      'Heals emotional wounds',
    ],
    category: 'mercy',
    isSaved: false,
  },
  {
    id: '3',
    name: 'الْمَلِكُ',
    arabic: 'الْمَلِكُ',
    transliteration: '<PERSON><PERSON><PERSON>',
    translation: 'The King',
    meaning:
      'The One with the complete dominion, the One whose dominion is clear from imperfection.',
    benefits: [
      'Brings dignity and honor',
      'Increases confidence',
      'Removes fear',
    ],
    category: 'sovereignty',
    isSaved: false,
  },
  {
    id: '4',
    name: 'الْقُدُّوسُ',
    arabic: 'الْقُدُّوسُ',
    transliteration: 'Al-Quddus',
    translation: 'The Most Sacred',
    meaning:
      'The One who is pure from any imperfection and clear from children and adversaries.',
    benefits: [
      'Purifies the soul',
      'Removes spiritual impurities',
      'Increases piety',
    ],
    category: 'purity',
    isSaved: false,
  },
  {
    id: '5',
    name: 'السَّلَامُ',
    arabic: 'السَّلَامُ',
    transliteration: 'As-Salaam',
    translation: 'The Source of Peace',
    meaning: 'The One who is free from every imperfection.',
    benefits: ['Brings inner peace', 'Removes anxiety', 'Calms the mind'],
    category: 'peace',
    isSaved: false,
  },
  {
    id: '6',
    name: 'الْمُؤْمِنُ',
    arabic: 'الْمُؤْمِنُ',
    transliteration: "Al-Mu'min",
    translation: 'The Giver of Security',
    meaning:
      'The One who witnessed for Himself that no one is God but Him. And He witnessed for His believers that they are truthful in their belief that no one is God but Him.',
    benefits: ['Increases faith', 'Provides security', 'Removes doubt'],
    category: 'faith',
    isSaved: false,
  },
  {
    id: '7',
    name: 'الْمُهَيْمِنُ',
    arabic: 'الْمُهَيْمِنُ',
    transliteration: 'Al-Muhaymin',
    translation: 'The Guardian',
    meaning: 'The One who witnesses the saying and deeds of His creatures.',
    benefits: ['Provides protection', 'Increases awareness', 'Brings guidance'],
    category: 'protection',
    isSaved: false,
  },
  {
    id: '8',
    name: 'الْعَزِيزُ',
    arabic: 'الْعَزِيزُ',
    transliteration: 'Al-Aziz',
    translation: 'The Mighty',
    meaning: 'The Strong, The Defeater who is not defeated.',
    benefits: ['Increases strength', 'Provides victory', 'Removes weakness'],
    category: 'strength',
    isSaved: false,
  },
  {
    id: '9',
    name: 'الْجَبَّارُ',
    arabic: 'الْجَبَّارُ',
    transliteration: 'Al-Jabbar',
    translation: 'The Compeller',
    meaning:
      'The One that nothing happens in His Dominion except that which He willed.',
    benefits: ['Removes oppression', 'Brings justice', 'Heals broken hearts'],
    category: 'power',
    isSaved: false,
  },
  {
    id: '10',
    name: 'الْمُتَكَبِّرُ',
    arabic: 'الْمُتَكَبِّرُ',
    transliteration: 'Al-Mutakabbir',
    translation: 'The Supreme',
    meaning:
      'The One who rejects the attributes of the creatures and accepts only the attributes of divinity.',
    benefits: [
      'Removes arrogance',
      'Increases humility',
      'Brings proper perspective',
    ],
    category: 'supremacy',
    isSaved: false,
  },
];
