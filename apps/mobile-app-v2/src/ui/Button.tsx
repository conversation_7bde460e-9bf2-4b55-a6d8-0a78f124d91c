import React from 'react';

import {
  ActivityIndicator,
  StyleProp,
  StyleSheet,
  TextStyle,
  TouchableOpacity,
  ViewStyle,
} from 'react-native';

import { Feather } from '@expo/vector-icons';

import { colors } from '../../constants/Colors';
import Theme from '../../constants/Theme';

import { Text } from './Text';
import { View } from './View';

export type ButtonProps = {
  title: string;
  onPress: () => void;
  variant?: 'primary' | 'secondary' | 'outline' | 'text' | 'emergency';
  size?: 'small' | 'medium' | 'large';
  disabled?: boolean;
  loading?: boolean;
  icon?: keyof typeof Feather.glyphMap;
  iconPosition?: 'left' | 'right';
  style?: StyleProp<ViewStyle>;
  textStyle?: StyleProp<TextStyle>;
  // Legacy props for backward compatibility
  leftIcon?: React.ReactElement;
  buttonStyle?: StyleProp<ViewStyle>;
};

export function Button({
  title,
  onPress,
  variant = 'primary',
  size = 'medium',
  disabled = false,
  loading = false,
  icon,
  iconPosition = 'left',
  style,
  textStyle,
  leftIcon,
  buttonStyle,
}: ButtonProps) {
  // Determine button styles based on variant
  const getButtonStyles = () => {
    switch (variant) {
      case 'primary':
        return {
          backgroundColor: disabled ? colors.disabled : colors.primary,
          borderColor: 'transparent',
        };
      case 'secondary':
        return {
          backgroundColor: disabled ? colors.disabled : colors.secondary,
          borderColor: 'transparent',
        };
      case 'outline':
        return {
          backgroundColor: 'transparent',
          borderColor: disabled ? colors.disabled : colors.primary,
          borderWidth: 1,
        };
      case 'text':
        return {
          backgroundColor: 'transparent',
          borderColor: 'transparent',
        };
      case 'emergency':
        return {
          backgroundColor: disabled ? colors.disabled : colors.emergencyRed,
          borderColor: 'transparent',
        };
      default:
        return {
          backgroundColor: disabled ? colors.disabled : colors.primary,
          borderColor: 'transparent',
        };
    }
  };

  // Determine text color based on variant
  const getTextColor = ():
    | keyof typeof colors.light
    | keyof typeof colors.dark => {
    if (disabled) {
      return 'textSecondary';
    }

    switch (variant) {
      case 'primary':
      case 'secondary':
      case 'emergency':
        return 'surface';
      case 'outline':
      case 'text':
        return 'primary';
      default:
        return 'surface';
    }
  };

  // Determine button size
  const getButtonSize = () => {
    switch (size) {
      case 'small':
        return {
          paddingVertical: Theme.spacing.xs,
          paddingHorizontal: Theme.spacing.m,
          borderRadius: Theme.borderRadius.small,
        };
      case 'medium':
        return {
          paddingVertical: Theme.spacing.s,
          paddingHorizontal: Theme.spacing.l,
          borderRadius: Theme.borderRadius.medium,
        };
      case 'large':
        return {
          paddingVertical: Theme.spacing.m,
          paddingHorizontal: Theme.spacing.xl,
          borderRadius: Theme.borderRadius.medium,
        };
      default:
        return {
          paddingVertical: Theme.spacing.s,
          paddingHorizontal: Theme.spacing.l,
          borderRadius: Theme.borderRadius.medium,
        };
    }
  };

  // Determine text size based on button size
  const getTextSize = () => {
    switch (size) {
      case 'small':
        return 's';
      case 'medium':
        return 'm';
      case 'large':
        return 'l';
      default:
        return 'm';
    }
  };

  // Determine icon size based on button size
  const getIconSize = () => {
    switch (size) {
      case 'small':
        return 16;
      case 'medium':
        return 20;
      case 'large':
        return 24;
      default:
        return 20;
    }
  };

  const buttonStyles = getButtonStyles();
  const textColor = getTextColor();
  const buttonSize = getButtonSize();
  const textSize = getTextSize();
  const iconSize = getIconSize();

  return (
    <TouchableOpacity
      onPress={onPress}
      disabled={disabled || loading}
      style={[styles.button, buttonStyles, buttonSize, style, buttonStyle]}
      activeOpacity={0.7}
    >
      <View backgroundColor="transparent" style={styles.content}>
        {loading ? (
          <ActivityIndicator
            size="small"
            color={
              variant === 'outline' || variant === 'text'
                ? colors.primary
                : colors.surface
            }
          />
        ) : (
          <>
            {leftIcon && <View style={styles.leftIcon}>{leftIcon}</View>}
            {!leftIcon && icon && iconPosition === 'left' && (
              <Feather name={icon} size={iconSize} style={styles.leftIcon} />
            )}
            <Text
              variant={textSize === 'l' ? 'subtitle' : 'body'}
              color={textColor}
              style={[
                styles.text,
                {
                  fontSize:
                    Theme.typography.fontSize[
                      textSize as keyof typeof Theme.typography.fontSize
                    ],
                },
                textStyle,
              ]}
            >
              {title}
            </Text>
            {icon && iconPosition === 'right' && (
              <Feather name={icon} size={iconSize} style={styles.rightIcon} />
            )}
          </>
        )}
      </View>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  button: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  text: {
    textAlign: 'center',
  },
  leftIcon: {
    marginRight: Theme.spacing.xs,
  },
  rightIcon: {
    marginLeft: Theme.spacing.xs,
  },
});
