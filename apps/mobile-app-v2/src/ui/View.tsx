import React from 'react';
import {
  View as RNView,
  StyleSheet,
  ViewStyle,
  StyleProp,
  ViewProps as RNViewProps,
} from 'react-native';
import { colors } from "../../constants/Colors";
import Theme from '../../constants/Theme';

export interface ViewProps extends RNViewProps {
  backgroundColor?:
    | keyof typeof colors.light
    | keyof typeof colors.dark
    | string;
  style?: StyleProp<ViewStyle>;
  withShadow?: boolean | 'small' | 'medium' | 'large';
  withBorder?: boolean;
  borderColor?: keyof typeof colors.light | keyof typeof colors.dark | string;
  borderRadius?: number | keyof typeof Theme.borderRadius;
}

export function View({
  backgroundColor = 'cardBackground',
  style,
  withShadow = false,
  withBorder = false,
  borderColor = 'border',
  borderRadius,
  ...otherProps
}: ViewProps) {
  // Determine background color - accept both theme color keys and direct color values
  const bgColor =
    typeof backgroundColor === 'string' && backgroundColor in colors
      ? colors[backgroundColor as keyof typeof colors]
      : backgroundColor;

  // Determine border color
  const borderColorValue =
    typeof borderColor === 'string' && borderColor in colors
      ? colors[borderColor as keyof typeof colors]
      : borderColor;

  // Determine shadow style based on withShadow prop
  const getShadowStyle = () => {
    if (!withShadow) return {};

    if (withShadow === true || withShadow === 'medium') {
      return Theme.shadows.medium;
    } else if (withShadow === 'small') {
      return Theme.shadows.small;
    } else if (withShadow === 'large') {
      return Theme.shadows.large;
    }

    return {};
  };

  // Determine border radius
  const getBorderRadius = () => {
    if (borderRadius === undefined) return undefined;

    if (typeof borderRadius === 'number') {
      return borderRadius;
    } else if (borderRadius in Theme.borderRadius) {
      return Theme.borderRadius[
        borderRadius as keyof typeof Theme.borderRadius
      ];
    }

    return undefined;
  };

  return (
    <RNView
      style={[
        styles.view,
        { backgroundColor: bgColor },
        withBorder && { borderWidth: 1, borderColor: borderColorValue },
        getShadowStyle(),
        getBorderRadius() !== undefined && { borderRadius: getBorderRadius() },
        style,
      ]}
      {...otherProps}
    />
  );
}

const styles = StyleSheet.create({
  view: {
    // Base view styles
  },
});
