import React from 'react';

import { StyleSheet } from 'react-native';

import { SpiritualResource } from '../types';

import { SoulLayerBadge } from './SoulLayerBadge';
import { Badge } from './ui/Badge';
import { Card } from './ui/Card';
import { IconButton } from './ui/IconButton';
import { Text } from './ui/Text';
import { View } from './ui/View';

type ResourceCardProps = {
  resource: SpiritualResource;
  onPress: () => void;
  onFavoriteToggle: () => void;
};

export function ResourceCard({ resource, onPress, onFavoriteToggle }: ResourceCardProps) {
  // Get icon based on resource type
  const getTypeIcon = (type: SpiritualResource['type']) => {
    switch (type) {
      case 'dua':
        return 'heart';
      case 'hadith':
        return 'book-open';
      case 'quran':
        return 'book';
      case 'article':
        return 'file-text';
      case 'video':
        return 'video';
      case 'audio':
        return 'headphones';
      default:
        return 'file';
    }
  };

  // Get formatted type label
  const getTypeLabel = (type: SpiritualResource['type']) => {
    return type.charAt(0).toUpperCase() + type.slice(1);
  };

  return (
    <Card
      style={styles.card}
      onPress={onPress}
    >
      <View style={styles.header}>
        <View style={styles.titleContainer}>
          <Badge
            label={getTypeLabel(resource.type)}
            variant="secondary"
            size="small"
            style={styles.typeBadge}
          />
          <Text variant="title" style={styles.title}>{resource.title}</Text>
        </View>
        <IconButton
          icon={resource.favorite ? 'heart' : 'heart-outline'}
          variant="ghost"
          size="small"
          onPress={onFavoriteToggle}
        />
      </View>

      <Text variant="body" style={styles.description}>
        {resource.description}
      </Text>

      <View style={styles.footer}>
        <View style={styles.badgesContainer}>
          {resource.soulLayers.map((layer) => (
            <SoulLayerBadge key={layer} layer={layer} size="small" />
          ))}
        </View>

        <View style={styles.tagsContainer}>
          {resource.tags.slice(0, 3).map((tag) => (
            <Badge
              key={tag}
              label={tag}
              variant="info"
              size="small"
              style={styles.tag}
            />
          ))}
        </View>
      </View>
    </Card>
  );
}

const styles = StyleSheet.create({
  card: {
    marginBottom: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  titleContainer: {
    flex: 1,
  },
  title: {
    marginTop: 4,
  },
  typeBadge: {
    marginBottom: 4,
  },
  description: {
    marginBottom: 16,
  },
  footer: {
    marginTop: 8,
  },
  badgesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 8,
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  tag: {
    marginRight: 8,
    marginBottom: 8,
  },
});
