import React from 'react';

import { StyleSheet, TouchableOpacity } from 'react-native';

import { Feather } from '@expo/vector-icons';

import { colors } from '../constants/Colors';
import Theme from '../constants/Theme';

import { Text } from './ui/Text';
import { View } from './ui/View';

// Journal entry category type
export type JournalCategory =
  | 'jism'
  | 'nafs'
  | 'aql'
  | 'qalb'
  | 'ruh'
  | 'general';

interface CategorySelectorProps {
  selectedCategory: JournalCategory;
  onSelectCategory: (category: JournalCategory) => void;
}

// Category options for journal entries
const CATEGORIES: {
  id: JournalCategory;
  label: string;
  description: string;
  icon: string;
}[] = [
  {
    id: 'jism',
    label: 'Body',
    description: 'Physical health and well-being',
    icon: 'activity',
  },
  {
    id: 'nafs',
    label: 'Emotions',
    description: 'Feelings and emotional states',
    icon: 'wind',
  },
  {
    id: 'aql',
    label: 'Mind',
    description: 'Thoughts and mental patterns',
    icon: 'cpu',
  },
  {
    id: 'qalb',
    label: 'Heart',
    description: 'Spiritual heart and inner qualities',
    icon: 'heart',
  },
  {
    id: 'ruh',
    label: 'Soul',
    description: 'Connection with <PERSON> and purpose',
    icon: 'star',
  },
  {
    id: 'general',
    label: 'General',
    description: 'Other reflections and notes',
    icon: 'book',
  },
];

export const CategorySelector = ({
  selectedCategory,
  onSelectCategory,
}: CategorySelectorProps) => {
  // Get color for category
  const getCategoryColor = (category: JournalCategory) => {
    switch (category) {
      case 'jism':
        return colors.error;
      case 'nafs':
        return colors.secondary;
      case 'aql':
        return colors.aqlYellow;
      case 'qalb':
        return colors.primary;
      case 'ruh':
        return colors.accent;
      default:
        return colors.primary;
    }
  };

  return (
    <View style={styles.container}>
      <Text variant="subtitle" style={styles.title}>
        Select a category
      </Text>

      <View style={styles.categoriesContainer}>
        {CATEGORIES.map((category) => {
          const isSelected = selectedCategory === category.id;
          const categoryColor = getCategoryColor(category.id);

          return (
            <TouchableOpacity
              key={category.id}
              style={[
                styles.categoryItem,
                {
                  backgroundColor: isSelected
                    ? categoryColor + '20'
                    : 'transparent',
                  borderColor: isSelected ? categoryColor : colors.border,
                },
              ]}
              onPress={() => onSelectCategory(category.id)}
            >
              <View
                style={[
                  styles.iconContainer,
                  { backgroundColor: categoryColor + '20' },
                ]}
              >
                <Feather name={category.icon as any} size={18} />
              </View>

              <View style={styles.categoryContent}>
                <Text
                  variant="subtitle"
                  style={{ color: isSelected ? categoryColor : colors.text }}
                >
                  {category.label}
                </Text>
                <Text variant="caption" color="textSecondary">
                  {category.description}
                </Text>
              </View>

              {isSelected && (
                <Feather
                  name="check-circle"
                  size={18}
                  style={styles.checkIcon}
                />
              )}
            </TouchableOpacity>
          );
        })}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: Theme.spacing.m,
  },
  title: {
    marginBottom: Theme.spacing.s,
  },
  categoriesContainer: {
    gap: Theme.spacing.s,
  },
  categoryItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: Theme.spacing.s,
    borderRadius: Theme.borderRadius.medium,
    borderWidth: 1,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: Theme.spacing.s,
  },
  categoryContent: {
    flex: 1,
  },
  checkIcon: {
    marginLeft: Theme.spacing.s,
  },
});
