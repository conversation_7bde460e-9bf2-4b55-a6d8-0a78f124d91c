import React from 'react';

import { StyleSheet, TouchableOpacity } from 'react-native';

import { Feather } from '@expo/vector-icons';

import { colors } from '../constants/Colors';
import Theme from '../constants/Theme';

import { Text } from './ui/Text';
import { View } from './ui/View';

type CategoryType = 'jism' | 'nafs' | 'aql' | 'qalb' | 'ruh';

interface CategoryTabProps {
  category: CategoryType;
  active: boolean;
  onPress: () => void;
}

export const CategoryTab = ({
  category,
  active,
  onPress,
}: CategoryTabProps) => {
  // Get category information
  const getCategoryInfo = () => {
    switch (category) {
      case 'jism':
        return {
          label: 'Jism',
          description: 'Body',
          icon: 'activity',
          color: colors.error,
        };
      case 'nafs':
        return {
          label: 'Nafs',
          description: 'Emotions',
          icon: 'wind',
          color: colors.secondary,
        };
      case 'aql':
        return {
          label: 'Aql',
          description: 'Mind',
          icon: 'cpu',
          color: colors.aqlYellow,
        };
      case 'qalb':
        return {
          label: 'Qalb',
          description: 'Heart',
          icon: 'heart',
          color: colors.primary,
        };
      case 'ruh':
        return {
          label: 'Ruh',
          description: 'Soul',
          icon: 'star',
          color: colors.accent,
        };
      default:
        return {
          label: 'Unknown',
          description: '',
          icon: 'help-circle',
          color: colors.primary,
        };
    }
  };

  const categoryInfo = getCategoryInfo();

  return (
    <TouchableOpacity
      style={[
        styles.container,
        active && {
          backgroundColor: categoryInfo.color + '20',
          borderColor: categoryInfo.color,
        },
        !active && {
          borderColor: colors.border,
        },
      ]}
      onPress={onPress}
      activeOpacity={0.7}
    >
      <View
        style={[
          styles.iconContainer,
          { backgroundColor: categoryInfo.color + '15' },
        ]}
      >
        <Feather name={categoryInfo.icon as any} size={22} />
      </View>
      <View style={styles.textContainer}>
        <Text
          variant="subtitle"
          style={{ color: active ? categoryInfo.color : colors.text }}
        >
          {categoryInfo.label}
        </Text>
        <Text variant="caption" color="textSecondary">
          {categoryInfo.description}
        </Text>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: Theme.spacing.m,
    paddingVertical: Theme.spacing.s,
    borderRadius: Theme.borderRadius.medium,
    borderWidth: 2,
    marginRight: Theme.spacing.s,
    marginBottom: Theme.spacing.s,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: Theme.spacing.s,
  },
  textContainer: {
    flexDirection: 'column',
  },
});
