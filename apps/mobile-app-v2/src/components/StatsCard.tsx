import React from 'react';

import { StyleSheet } from 'react-native';

import { Feather } from '@expo/vector-icons';

import { colors } from '../constants/Colors';
import Theme from '../constants/Theme';

import { Card } from './ui/Card';
import { Text } from './ui/Text';
import { View } from './ui/View';

interface StatItemProps {
  label: string;
  value: number | string;
  icon: string;
  color?: string;
}

interface StatsCardProps {
  title: string;
  stats: StatItemProps[];
}

export const StatItem = ({ label, value, icon, color }: StatItemProps) => {
  const iconColor = color || colors.primary;

  return (
    <View style={styles.statItem}>
      <View
        style={[styles.iconContainer, { backgroundColor: iconColor + '15' }]}
      ></View>
      <View style={styles.statContent}>
        <Text variant="heading3" style={styles.statValue}>
          {value}
        </Text>
        <Text variant="caption" color="textSecondary" style={styles.statLabel}>
          {label}
        </Text>
      </View>
    </View>
  );
};

export const StatsCard = ({ title, stats }: StatsCardProps) => {
  return (
    <Card style={styles.card}>
      <Text variant="subtitle" style={styles.title}>
        {title}
      </Text>

      <View style={styles.statsGrid}>
        {stats.map((stat, index) => (
          <StatItem
            key={index}
            label={stat.label}
            value={stat.value}
            icon={stat.icon}
          />
        ))}
      </View>
    </Card>
  );
};

const styles = StyleSheet.create({
  card: {
    marginBottom: Theme.spacing.m,
  },
  title: {
    marginBottom: Theme.spacing.m,
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  statItem: {
    width: '48%',
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Theme.spacing.m,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: Theme.spacing.s,
  },
  statContent: {
    flex: 1,
  },
  statValue: {
    marginBottom: 2,
  },
  statLabel: {
    fontSize: 12,
  },
});
