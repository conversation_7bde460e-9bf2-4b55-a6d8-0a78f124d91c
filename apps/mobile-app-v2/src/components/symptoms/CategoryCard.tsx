import { AntDesign, MaterialCommunityIcons } from '@expo/vector-icons';
import React, { useState } from 'react';
import {
  Animated,
  LayoutAnimation,
  Platform,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  UIManager,
  View,
} from 'react-native';
import { colors } from '../../constants/Colors';

// Enable LayoutAnimation for Android
if (Platform.OS === 'android') {
  if (UIManager.setLayoutAnimationEnabledExperimental) {
    UIManager.setLayoutAnimationEnabledExperimental(true);
  }
}

// Category information for the five layers
export const CATEGORIES = {
  jism: {
    title: 'Jism (Body)',
    icon: 'human-male',
    color: '#FF725C',
    description: 'Physical symptoms affecting your body and daily functioning.',
  },
  nafs: {
    title: 'Nafs (Emotions)',
    icon: 'emoticon-outline',
    color: '#FFB54D',
    description:
      'Emotional states and challenges that affect your inner peace.',
  },
  aql: {
    title: 'Aql (Mind)',
    icon: 'brain',
    color: '#6ECFF6',
    description:
      'Thought patterns and mental processes that shape your perception.',
  },
  qalb: {
    title: 'Qalb (Heart)',
    icon: 'heart-outline',
    color: '#9775FA',
    description: 'Spiritual connection and states of your inner heart.',
  },
  ruh: {
    title: 'Ruh (Soul)',
    icon: 'white-balance-sunny',
    color: '#4CAF50',
    description: 'Deep spiritual matters related to your soul and purpose.',
  },
};

interface CategoryCardProps {
  /**
   * Category key (jism, nafs, aql, qalb, ruh)
   */
  category: keyof typeof CATEGORIES;

  /**
   * Available symptoms for this category
   */
  symptoms: string[];

  /**
   * Currently selected symptoms
   */
  selectedSymptoms: string[];

  /**
   * Called when a symptom is selected or deselected
   */
  onSelectSymptom: (symptom: string) => void;

  /**
   * Current intensity value for this category
   */
  intensity: number;

  /**
   * Called when the intensity changes
   */
  onIntensityChange: (value: number) => void;

  /**
   * Called when the user wants to add a custom symptom
   */
  onAddCustomSymptom?: (category: keyof typeof CATEGORIES) => void;
}

/**
 * A card component for selecting symptoms within a category
 */
export default function CategoryCard({
  category,
  symptoms,
  selectedSymptoms,
  onSelectSymptom,
  intensity,
  onIntensityChange,
  onAddCustomSymptom,
}: CategoryCardProps) {
  const [expanded, setExpanded] = useState(false);
  const [fadeAnim] = useState(new Animated.Value(0));
  const { title, icon, color, description } = CATEGORIES[category];
  const styles = createStyles(colors);

  // Toggle expanded state with animation
  const toggleExpanded = () => {
    LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut);
    setExpanded(!expanded);

    Animated.timing(fadeAnim, {
      toValue: expanded ? 0 : 1,
      duration: 300,
      useNativeDriver: true,
    }).start();
  };

  // Check if any symptoms are selected
  const hasSelectedSymptoms = selectedSymptoms.length > 0;

  return (
    <View style={[styles.container, { borderColor: color }]}>
      <TouchableOpacity
        style={[
          styles.header,
          { backgroundColor: hasSelectedSymptoms ? color : 'white' },
        ]}
        onPress={toggleExpanded}
        activeOpacity={0.8}
      >
        <View style={styles.titleContainer}>
          <MaterialCommunityIcons
            name={icon as any}
            size={24}
            style={styles.icon}
          />
          <Text
            style={[
              styles.title,
              { color: hasSelectedSymptoms ? 'white' : colors.text },
            ]}
          >
            {title}
          </Text>
        </View>

        <View style={styles.headerRight}>
          {hasSelectedSymptoms && (
            <View style={styles.countBadge}>
              <Text style={styles.countText}>{selectedSymptoms.length}</Text>
            </View>
          )}
          <AntDesign name={expanded ? 'up' : 'down'} size={16} />
        </View>
      </TouchableOpacity>

      {expanded && (
        <Animated.View
          style={[
            styles.content,
            {
              opacity: fadeAnim,
              transform: [
                {
                  translateY: fadeAnim.interpolate({
                    inputRange: [0, 1],
                    outputRange: [10, 0],
                  }),
                },
              ],
            },
          ]}
        >
          <Text style={styles.description}>{description}</Text>

          <Text style={styles.sectionTitle}>Select all that apply:</Text>

          <ScrollView
            style={styles.symptomsContainer}
            contentContainerStyle={styles.symptomsContent}
            showsVerticalScrollIndicator={false}
          >
            {symptoms.map((symptom) => (
              <TouchableOpacity
                key={symptom}
                style={[
                  styles.symptomButton,
                  selectedSymptoms.includes(symptom) && {
                    backgroundColor: color,
                    borderColor: color,
                  },
                ]}
                onPress={() => onSelectSymptom(symptom)}
                activeOpacity={0.7}
              >
                <Text
                  style={[
                    styles.symptomText,
                    selectedSymptoms.includes(symptom) && { color: 'white' },
                  ]}
                >
                  {symptom}
                </Text>
                {selectedSymptoms.includes(symptom) && (
                  <AntDesign name="check" size={16} color="white" />
                )}
              </TouchableOpacity>
            ))}

            {onAddCustomSymptom && (
              <TouchableOpacity
                style={styles.addCustomButton}
                onPress={() => onAddCustomSymptom(category)}
              >
                <Text style={[styles.addCustomText, { color }]}>
                  Add custom
                </Text>
              </TouchableOpacity>
            )}
          </ScrollView>

          {hasSelectedSymptoms && (
            <View style={styles.intensityContainer}>
              <Text style={styles.intensityTitle}>How intense is this?</Text>
              <View style={styles.sliderContainer}>
                <Text style={styles.sliderLabel}>Mild</Text>
                <View style={styles.slider}>
                  {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map((value) => (
                    <TouchableOpacity
                      key={value}
                      style={[
                        styles.sliderButton,
                        intensity === value && {
                          backgroundColor: color,
                          borderColor: color,
                          transform: [{ scale: 1.2 }],
                        },
                      ]}
                      onPress={() => onIntensityChange(value)}
                    >
                      {intensity === value && (
                        <Text style={styles.sliderButtonText}>{value}</Text>
                      )}
                    </TouchableOpacity>
                  ))}
                </View>
                <Text style={styles.sliderLabel}>Severe</Text>
              </View>
            </View>
          )}
        </Animated.View>
      )}
    </View>
  );
}

const createStyles = (colors: ColorTheme) =>
  StyleSheet.create({
    container: {
      borderRadius: 8,
      borderWidth: 1,
      marginVertical: 8,
      overflow: 'hidden',
      backgroundColor: 'white',
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 4,
      elevation: 2,
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      padding: 16,
      borderRadius: 8,
    },
    titleContainer: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    icon: {
      marginRight: 8,
    },
    title: {
      fontSize: 18,
      fontWeight: '600',
    },
    headerRight: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    countBadge: {
      backgroundColor: 'rgba(255, 255, 255, 0.3)',
      borderRadius: 12,
      paddingHorizontal: 8,
      paddingVertical: 2,
      marginRight: 8,
    },
    countText: {
      color: 'white',
      fontSize: 12,
      fontWeight: 'bold',
    },
    content: {
      padding: 16,
    },
    description: {
      fontSize: 14,
      color: colors.textSecondary,
      marginBottom: 16,
    },
    sectionTitle: {
      fontSize: 16,
      fontWeight: '600',
      marginBottom: 8,
      color: colors.text,
    },
    symptomsContainer: {
      maxHeight: 180,
    },
    symptomsContent: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      paddingBottom: 8,
    },
    symptomButton: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      borderWidth: 1,
      borderColor: '#DDDDDD',
      borderRadius: 20,
      paddingHorizontal: 12,
      paddingVertical: 8,
      margin: 4,
      backgroundColor: '#F9F9F9',
    },
    symptomText: {
      fontSize: 14,
      marginRight: 8,
      color: colors.text,
    },
    addCustomButton: {
      flexDirection: 'row',
      alignItems: 'center',
      borderWidth: 1,
      borderColor: '#DDDDDD',
      borderStyle: 'dashed',
      borderRadius: 20,
      paddingHorizontal: 12,
      paddingVertical: 8,
      margin: 4,
      backgroundColor: 'white',
    },
    addCustomText: {
      fontSize: 14,
      marginLeft: 4,
    },
    intensityContainer: {
      marginTop: 16,
      paddingTop: 16,
      borderTopWidth: 1,
      borderTopColor: '#EEEEEE',
    },
    intensityTitle: {
      fontSize: 16,
      fontWeight: '600',
      marginBottom: 8,
      color: colors.text,
    },
    sliderContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      marginTop: 8,
    },
    sliderLabel: {
      fontSize: 12,
      color: colors.textSecondary,
      width: 40,
    },
    slider: {
      flex: 1,
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginHorizontal: 8,
    },
    sliderButton: {
      width: 24,
      height: 24,
      borderRadius: 12,
      borderWidth: 1,
      borderColor: '#DDDDDD',
      backgroundColor: '#F9F9F9',
      alignItems: 'center',
      justifyContent: 'center',
    },
    sliderButtonText: {
      fontSize: 10,
      color: 'white',
      fontWeight: 'bold',
    },
  });
