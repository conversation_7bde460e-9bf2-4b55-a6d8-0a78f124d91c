import { AntDesign } from "@expo/vector-icons";
import React, { useEffect, useState } from "react";
import {
  ActivityIndicator,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from "react-native";
import { SymptomSubmission } from "../../services/api/types";
import serviceRegistry from "../../services/ServiceRegistry";
import { useSymptoms } from "../../state/context";
import CategoryCard, { CATEGORIES } from "./CategoryCard";
import DurationSelector from "./DurationSelector";

// Duration options for symptoms
const DURATION_OPTIONS = [
  { label: "Less than a week", value: "less_than_week" },
  { label: "1-4 weeks", value: "1-4_weeks" },
  { label: "1-3 months", value: "1-3_months" },
  { label: "3-6 months", value: "3-6_months" },
  { label: "6-12 months", value: "6-12_months" },
  { label: "More than a year", value: "more_than_year" },
];

interface SymptomSelectorProps {
  /**
   * Called when submission is complete
   */
  onSubmit: (submission: SymptomSubmission, diagnosis: any) => void;

  /**
   * Called when the user cancels
   */
  onCancel?: () => void;

  /**
   * Initial symptoms (for editing existing submission)
   */
  initialSymptoms?: Partial<SymptomSubmission>;
}

/**
 * A comprehensive component for selecting symptoms across the five categories
 */
export default function SymptomSelector({
  onSubmit,
  onCancel,
  initialSymptoms,
}: SymptomSelectorProps) {
  // Symptoms context
  const { state: symptomsState, fetchSymptoms, submitSymptoms } = useSymptoms();
  // State for selected symptoms
  const [selectedSymptoms, setSelectedSymptoms] = useState<{
    jism: string[];
    nafs: string[];
    aql: string[];
    qalb: string[];
    ruh: string[];
  }>({
    jism: initialSymptoms?.jism || [],
    nafs: initialSymptoms?.nafs || [],
    aql: initialSymptoms?.aql || [],
    qalb: initialSymptoms?.qalb || [],
    ruh: initialSymptoms?.ruh || [],
  });

  // State for intensity ratings
  const [intensity, setIntensity] = useState<{
    jism: number;
    nafs: number;
    aql: number;
    qalb: number;
    ruh: number;
  }>({
    jism: initialSymptoms?.intensity?.jism || 5,
    nafs: initialSymptoms?.intensity?.nafs || 5,
    aql: initialSymptoms?.intensity?.aql || 5,
    qalb: initialSymptoms?.intensity?.qalb || 5,
    ruh: initialSymptoms?.intensity?.ruh || 5,
  });

  // State for duration
  const [duration, setDuration] = useState<string>(
    initialSymptoms?.duration || "1-4_weeks"
  );

  // State for available symptoms
  const [availableSymptoms, setAvailableSymptoms] = useState<{
    jism: string[];
    nafs: string[];
    aql: string[];
    qalb: string[];
    ruh: string[];
  }>({
    jism: [],
    nafs: [],
    aql: [],
    qalb: [],
    ruh: [],
  });

  // Loading and error states
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [submitting, setSubmitting] = useState<boolean>(false);

  // Custom symptom modal state
  const [customSymptomModal, setCustomSymptomModal] = useState<{
    visible: boolean;
    category: keyof typeof CATEGORIES | null;
    value: string;
  }>({
    visible: false,
    category: null,
    value: "",
  });

  // Fetch available symptoms on mount
  useEffect(() => {
    if (symptomsState.categories.length === 0) {
      fetchSymptoms();
    } else {
      // Convert categories to the format expected by this component
      const categoriesMap: {
        jism: string[];
        nafs: string[];
        aql: string[];
        qalb: string[];
        ruh: string[];
      } = {
        jism: [],
        nafs: [],
        aql: [],
        qalb: [],
        ruh: [],
      };

      symptomsState.categories.forEach((category) => {
        if (categoriesMap[category.id as keyof typeof categoriesMap]) {
          categoriesMap[category.id as keyof typeof categoriesMap] =
            category.symptoms;
        }
      });

      setAvailableSymptoms(categoriesMap);
    }
  }, [symptomsState.categories]);

  // Update loading and error states from context
  useEffect(() => {
    setLoading(symptomsState.loading);
    setError(symptomsState.error);
  }, [symptomsState.loading, symptomsState.error]);

  // Handle symptom selection/deselection
  const handleSelectSymptom = (
    category: keyof typeof CATEGORIES,
    symptom: string
  ) => {
    setSelectedSymptoms((prev) => {
      const categorySymptoms = [...prev[category]];

      if (categorySymptoms.includes(symptom)) {
        // Remove symptom if already selected
        return {
          ...prev,
          [category]: categorySymptoms.filter((s) => s !== symptom),
        };
      } else {
        // Add symptom if not already selected
        return {
          ...prev,
          [category]: [...categorySymptoms, symptom],
        };
      }
    });
  };

  // Handle intensity change
  const handleIntensityChange = (
    category: keyof typeof CATEGORIES,
    value: number
  ) => {
    setIntensity((prev) => ({
      ...prev,
      [category]: value,
    }));
  };

  // Open custom symptom modal
  const handleAddCustomSymptom = (category: keyof typeof CATEGORIES) => {
    setCustomSymptomModal({
      visible: true,
      category,
      value: "",
    });
  };

  // Add custom symptom
  const addCustomSymptom = () => {
    const { category, value } = customSymptomModal;

    if (category && value.trim()) {
      // Add to selected symptoms
      handleSelectSymptom(category, value.trim());

      // Close modal
      setCustomSymptomModal({
        visible: false,
        category: null,
        value: "",
      });
    }
  };

  // Cancel custom symptom
  const cancelCustomSymptom = () => {
    setCustomSymptomModal({
      visible: false,
      category: null,
      value: "",
    });
  };

  // Check if any symptoms are selected
  const hasSelectedSymptoms = () => {
    return Object.values(selectedSymptoms).some(
      (symptoms) => symptoms.length > 0
    );
  };

  // Handle submission
  const handleSubmit = async () => {
    if (!hasSelectedSymptoms()) {
      setError("Please select at least one symptom");
      return;
    }

    try {
      setSubmitting(true);
      setError(null);

      // Prepare submission data
      const submission: SymptomSubmission = {
        jism: selectedSymptoms.jism,
        nafs: selectedSymptoms.nafs,
        aql: selectedSymptoms.aql,
        qalb: selectedSymptoms.qalb,
        ruh: selectedSymptoms.ruh,
        intensity: {
          jism: intensity.jism,
          nafs: intensity.nafs,
          aql: intensity.aql,
          qalb: intensity.qalb,
          ruh: intensity.ruh,
        },
        duration,
      };

      // Submit using context
      const response = await submitSymptoms(submission);

      // Call onSubmit with response
      onSubmit(submission, response.diagnosis);
    } catch (err) {
      console.error("Failed to submit symptoms:", err);
      setError("Failed to submit symptoms. Please try again.");
    } finally {
      setSubmitting(false);
    }
  };

  // Get total selected symptoms count
  const getSelectedSymptomsCount = () => {
    return Object.values(selectedSymptoms).reduce(
      (total, symptoms) => total + symptoms.length,
      0
    );
  };

  // Handle duration selection
  const handleSelectDuration = (value: string) => {
    setDuration(value);
  };

  // Render loading state
  if (loading) {
    return (
      <View style={styles.centerContainer}>
        <ActivityIndicator size="large" color="#007AFF" />
        <Text style={styles.loadingText}>Loading symptom categories...</Text>
      </View>
    );
  }

  // Render error state
  if (error && !loading && !submitting) {
    return (
      <View style={styles.centerContainer}>
        <AntDesign name="warning" size={48} color="#d32f2f" />
        <Text style={styles.errorText}>{error}</Text>
        <TouchableOpacity
          style={styles.retryButton}
          onPress={() => {
            setLoading(true);
            // Re-fetch symptoms
            serviceRegistry
              .getSymptoms()
              .then((service) => service.getSymptomCategories())
              .then((categories) => {
                setAvailableSymptoms({
                  jism: categories.jism || [],
                  nafs: categories.nafs || [],
                  aql: categories.aql || [],
                  qalb: categories.qalb || [],
                  ruh: categories.ruh || [],
                });
                setError(null);
              })
              .catch((err) => {
                console.error("Retry failed:", err);
                setError(
                  "Failed to load symptom categories. Please try again."
                );
              })
              .finally(() => setLoading(false));
          }}
        >
          <Text style={styles.retryButtonText}>Retry</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === "ios" ? "padding" : "height"}
      keyboardVerticalOffset={100}
    >
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        <Text style={styles.title}>What are you experiencing?</Text>
        <Text style={styles.subtitle}>
          Select all symptoms you're experiencing in each category
        </Text>

        {/* Jism Category */}
        <CategoryCard
          category="jism"
          symptoms={availableSymptoms.jism}
          selectedSymptoms={selectedSymptoms.jism}
          onSelectSymptom={(symptom) => handleSelectSymptom("jism", symptom)}
          intensity={intensity.jism}
          onIntensityChange={(value) => handleIntensityChange("jism", value)}
          onAddCustomSymptom={handleAddCustomSymptom}
        />

        {/* Nafs Category */}
        <CategoryCard
          category="nafs"
          symptoms={availableSymptoms.nafs}
          selectedSymptoms={selectedSymptoms.nafs}
          onSelectSymptom={(symptom) => handleSelectSymptom("nafs", symptom)}
          intensity={intensity.nafs}
          onIntensityChange={(value) => handleIntensityChange("nafs", value)}
          onAddCustomSymptom={handleAddCustomSymptom}
        />

        {/* Aql Category */}
        <CategoryCard
          category="aql"
          symptoms={availableSymptoms.aql}
          selectedSymptoms={selectedSymptoms.aql}
          onSelectSymptom={(symptom) => handleSelectSymptom("aql", symptom)}
          intensity={intensity.aql}
          onIntensityChange={(value) => handleIntensityChange("aql", value)}
          onAddCustomSymptom={handleAddCustomSymptom}
        />

        {/* Qalb Category */}
        <CategoryCard
          category="qalb"
          symptoms={availableSymptoms.qalb}
          selectedSymptoms={selectedSymptoms.qalb}
          onSelectSymptom={(symptom) => handleSelectSymptom("qalb", symptom)}
          intensity={intensity.qalb}
          onIntensityChange={(value) => handleIntensityChange("qalb", value)}
          onAddCustomSymptom={handleAddCustomSymptom}
        />

        {/* Ruh Category */}
        <CategoryCard
          category="ruh"
          symptoms={availableSymptoms.ruh}
          selectedSymptoms={selectedSymptoms.ruh}
          onSelectSymptom={(symptom) => handleSelectSymptom("ruh", symptom)}
          intensity={intensity.ruh}
          onIntensityChange={(value) => handleIntensityChange("ruh", value)}
          onAddCustomSymptom={handleAddCustomSymptom}
        />

        {/* Duration Section */}
        <DurationSelector value={duration} onChange={handleSelectDuration} />
      </ScrollView>

      {/* Submit Button */}
      <View style={styles.submitContainer}>
        <TouchableOpacity
          style={[
            styles.submitButton,
            (!hasSelectedSymptoms() || submitting) &&
              styles.submitButtonDisabled,
          ]}
          onPress={handleSubmit}
          disabled={!hasSelectedSymptoms() || submitting}
          activeOpacity={0.8}
        >
          <Text style={styles.submitButtonText}>
            {submitting ? "Analyzing..." : "Analyze Symptoms"}
          </Text>
        </TouchableOpacity>
      </View>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#fff",
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
  },
  title: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#1a1a1a",
    textAlign: "center",
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: "#666",
    textAlign: "center",
    marginBottom: 24,
  },
  centerContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 20,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: "#666",
  },
  errorText: {
    marginTop: 16,
    fontSize: 16,
    color: "#d32f2f",
    textAlign: "center",
  },
  retryButton: {
    marginTop: 16,
    paddingHorizontal: 24,
    paddingVertical: 12,
    backgroundColor: "#007AFF",
    borderRadius: 8,
  },
  retryButtonText: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "600",
  },
  submitContainer: {
    padding: 16,
    backgroundColor: "#fff",
    borderTopWidth: 1,
    borderTopColor: "#e0e0e0",
  },
  submitButton: {
    backgroundColor: "#007AFF",
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: "center",
  },
  submitButtonDisabled: {
    backgroundColor: "#ccc",
  },
  submitButtonText: {
    color: "#fff",
    fontSize: 18,
    fontWeight: "600",
  },
});
