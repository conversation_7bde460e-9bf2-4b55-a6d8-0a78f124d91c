import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  Switch,
  StyleSheet,
  ActivityIndicator,
  Pressable,
  Platform,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import dataSourceProvider from '../services/data/DataSourceProvider';
import { colors } from '../constants/Colors';

interface DataSourceIndicatorProps {
  /**
   * Show compact version (icon only) or full version with text
   */
  compact?: boolean;

  /**
   * Additional styles for the container
   */
  style?: object;

  /**
   * Callback function to be called when data source changes
   */
  onSourceChange?: (isUsingRealApi: boolean) => void;
}

/**
 * DataSourceIndicator Component
 *
 * Displays the current data source (API or dummy data) and
 * allows the user to toggle between them.
 */
export default function DataSourceIndicator({
  compact = false,
  style = {},
  onSourceChange,
}: DataSourceIndicatorProps) {
  // State for tracking the current data source
  const [isUsingRealApi, setIsUsingRealApi] = useState(false);

  // State for tracking loading state during source switching
  const [isLoading, setIsLoading] = useState(false);

  // State for expanded view (when pressed in compact mode)
  const [isExpanded, setIsExpanded] = useState(false);

  // Load the initial data source setting
  useEffect(() => {
    const loadDataSourceSetting = async () => {
      try {
        const usingRealApi = await dataSourceProvider.isUsingRealApi();
        setIsUsingRealApi(usingRealApi);
      } catch (error) {
        console.error('Failed to load data source setting:', error);
      }
    };

    loadDataSourceSetting();
  }, []);

  // Handle toggling the data source
  const toggleDataSource = async () => {
    try {
      setIsLoading(true);

      // Toggle to the opposite of current state
      const newValue = !isUsingRealApi;

      // Update the data source provider
      dataSourceProvider.toggleDataSource();

      // Update local state
      setIsUsingRealApi(newValue);

      // Call the callback if provided
      if (onSourceChange) {
        onSourceChange(newValue);
      }
    } catch (error) {
      console.error('Failed to toggle data source:', error);

      // Show error feedback here if needed
    } finally {
      setIsLoading(false);

      // Auto-collapse expanded view after selection
      if (isExpanded) {
        setTimeout(() => setIsExpanded(false), 1500);
      }
    }
  };

  // Handle pressing the indicator in compact mode
  const handlePress = () => {
    if (compact) {
      setIsExpanded(!isExpanded);
    }
  };

  // Render loading indicator during switching
  if (isLoading) {
    return (
      <View style={[styles.container, style]}>
        {!compact && (
          <Text style={styles.loadingText}>Switching data source...</Text>
        )}
      </View>
    );
  }

  // Render compact version (icon only)
  if (compact && !isExpanded) {
    return (
      <Pressable style={[styles.compactContainer, style]} onPress={handlePress}>
        <Ionicons name={isUsingRealApi ? 'cloud' : 'document-text'} size={24} />
        <View
          style={[
            styles.statusDot,
            {
              backgroundColor: isUsingRealApi ? colors.success : colors.warning,
            },
          ]}
        />
      </Pressable>
    );
  }

  // Render the full or expanded indicator
  return (
    <Pressable
      style={[
        styles.container,
        compact && styles.expandedCompactContainer,
        style,
      ]}
      onPress={handlePress}
    >
      <View style={styles.labelContainer}>
        <Ionicons
          name={isUsingRealApi ? 'cloud' : 'document-text'}
          size={20}
          style={styles.icon}
        />
        <Text style={styles.sourceText}>
          {isUsingRealApi ? 'Using: API Data' : 'Using: Dummy Data'}
        </Text>
      </View>
      <Switch
        trackColor={{ false: '#767577', true: colors.primaryLight }}
        thumbColor={isUsingRealApi ? colors.primary : '#f4f3f4'}
        ios_backgroundColor="#767577"
        onValueChange={toggleDataSource}
        value={isUsingRealApi}
      />
    </Pressable>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: 8,
    padding: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    marginVertical: 8,
    marginHorizontal: 16,
  },
  compactContainer: {
    position: 'absolute',
    top: Platform.OS === 'ios' ? 50 : 20,
    right: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: 20,
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 3,
    elevation: 5,
    zIndex: 999,
  },
  expandedCompactContainer: {
    position: 'absolute',
    top: Platform.OS === 'ios' ? 50 : 20,
    right: 20,
    zIndex: 999,
    width: 220,
  },
  labelContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  icon: {
    marginRight: 8,
  },
  sourceText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
  },
  loadingText: {
    fontSize: 14,
    color: '#666',
    marginLeft: 10,
  },
  statusDot: {
    position: 'absolute',
    top: 8,
    right: 8,
    width: 10,
    height: 10,
    borderRadius: 5,
    borderWidth: 1,
    borderColor: 'white',
  },
});
