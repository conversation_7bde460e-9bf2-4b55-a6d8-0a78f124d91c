import React, { useEffect } from 'react';
import { StyleSheet, TouchableOpacity, View } from 'react-native';
import { Feather } from '@expo/vector-icons';

import { QuranVerse } from '../types';
import { Text } from './ui/Text';
import { colors } from '../constants/Colors';
import Theme from '../constants/Theme';

import { useAudioPlayer } from '../hooks/useAudioPlayer';

interface QuranVerseCardProps {
  verse: QuranVerse;
  isSaved: boolean;
  onSave: () => void;
}

export const QuranVerseCard: React.FC<QuranVerseCardProps> = ({
  verse,
  isSaved,
  onSave,
}) => {
  const audioPlayer = useAudioPlayer(verse.audioUrl);

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <View>
          <Text variant="subtitle">{verse.surah}</Text>
          <Text variant="caption" color="textSecondary">
            Ayah {verse.ayah}
          </Text>
        </View>
        <TouchableOpacity
          style={[
            styles.saveButton,
            isSaved && { backgroundColor: colors.primary },
          ]}
          onPress={onSave}
        >
          <Feather name={isSaved ? 'bookmark' : 'bookmark-outline'} size={18} />
        </TouchableOpacity>
      </View>

      <Text style={styles.arabicText}>{verse.arabicText}</Text>

      <Text variant="body" color="textSecondary" style={styles.translation}>
        "{verse.translation}"
      </Text>

      <Text variant="caption" style={styles.transliteration}>
        {verse.transliteration}
      </Text>

      <View style={styles.divider} />

      <Text variant="caption" color="textSecondary" style={styles.tafsir}>
        {verse.tafsir}
      </Text>

      {verse.audioUrl && (
        <>
          <TouchableOpacity
            style={[
              styles.audioButton,
              {
                backgroundColor: audioPlayer.error
                  ? colors.error
                  : colors.accent,
              },
            ]}
            onPress={
              audioPlayer.error
                ? audioPlayer.loadAudio
                : audioPlayer.togglePlayPause
            }
            disabled={audioPlayer.isLoading}
          >
            <Feather
              name={
                audioPlayer.error
                  ? 'refresh-cw'
                  : audioPlayer.isPlaying
                  ? 'pause'
                  : 'play'
              }
              size={16}
              color="#fff"
            />
            <Text variant="caption" color="surface" style={styles.audioText}>
              {audioPlayer.isLoading
                ? 'Loading...'
                : audioPlayer.error
                ? 'Retry'
                : audioPlayer.isPlaying
                ? 'Pause Recitation'
                : 'Listen to Recitation'}
            </Text>
          </TouchableOpacity>

          {audioPlayer.error && (
            <Text variant="caption" color="error" style={styles.errorText}>
              Audio failed to load. Please try again.
            </Text>
          )}
        </>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: Theme.spacing.m,
    backgroundColor: '#fff',
    borderRadius: Theme.borderRadius.medium,
    ...Theme.shadows.small,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Theme.spacing.m,
  },
  saveButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: colors.primary,
  },
  arabicText: {
    fontSize: 24,
    fontFamily: Theme.typography.fontFamily.arabic,
    textAlign: 'right',
    marginBottom: Theme.spacing.m,
    lineHeight: Theme.typography.lineHeight.loose,
  },
  translation: {
    marginBottom: Theme.spacing.s,
    fontStyle: 'italic',
  },
  transliteration: {
    marginBottom: Theme.spacing.m,
  },
  divider: {
    height: 1,
    backgroundColor: '#f0f0f0',
    marginVertical: Theme.spacing.m,
  },
  tafsir: {
    marginBottom: Theme.spacing.m,
    lineHeight: Theme.typography.lineHeight.normal,
  },
  audioButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: Theme.spacing.s,
    borderRadius: Theme.borderRadius.pill,
  },
  audioText: {
    marginLeft: Theme.spacing.xs,
  },
  errorText: {
    marginTop: Theme.spacing.xs,
    textAlign: 'center',
  },
});
