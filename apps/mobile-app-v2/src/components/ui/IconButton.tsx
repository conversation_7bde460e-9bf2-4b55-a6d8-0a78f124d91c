import React from 'react';

import {
  TouchableOpacity,
  StyleSheet,
  ActivityIndicator,
  StyleProp,
  ViewStyle,
} from 'react-native';

import { Feather } from '@expo/vector-icons';

import { colors } from "../../constants/Colors";
import Theme from '../../constants/Theme';

import { View } from './View';

export type IconButtonProps = {
  icon: keyof typeof Feather.glyphMap;
  onPress: () => void;
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'emergency';
  size?: 'small' | 'medium' | 'large';
  disabled?: boolean;
  loading?: boolean;
  style?: StyleProp<ViewStyle>;
  accessibilityLabel?: string;
};

export function IconButton({
  icon,
  onPress,
  variant = 'primary',
  size = 'medium',
  disabled = false,
  loading = false,
  style,
  accessibilityLabel,
}: IconButtonProps) {
  // Determine button styles based on variant
  const getButtonStyles = () => {
    switch (variant) {
      case 'primary':
        return {
          backgroundColor: disabled ? colors.disabled : colors.primary,
          borderColor: 'transparent',
        };
      case 'secondary':
        return {
          backgroundColor: disabled ? colors.disabled : colors.secondary,
          borderColor: 'transparent',
        };
      case 'outline':
        return {
          backgroundColor: 'transparent',
          borderColor: disabled ? colors.disabled : colors.primary,
          borderWidth: 1,
        };
      case 'ghost':
        return {
          backgroundColor: 'transparent',
          borderColor: 'transparent',
        };
      case 'emergency':
        return {
          backgroundColor: disabled ? colors.disabled : colors.emergencyRed,
          borderColor: 'transparent',
        };
      default:
        return {
          backgroundColor: disabled ? colors.disabled : colors.primary,
          borderColor: 'transparent',
        };
    }
  };

  // Determine icon color based on variant
  const getIconColor = ():
    | keyof typeof colors.light
    | keyof typeof colors.dark => {
    if (disabled) {
      return 'textSecondary';
    }

    switch (variant) {
      case 'primary':
      case 'secondary':
      case 'emergency':
        return 'surface';
      case 'outline':
      case 'ghost':
        return 'primary';
      default:
        return 'surface';
    }
  };

  // Determine button size and icon size
  const getButtonSize = () => {
    switch (size) {
      case 'small':
        return {
          padding: Theme.spacing.xs,
          borderRadius: Theme.borderRadius.small,
          iconSize: 16,
        };
      case 'medium':
        return {
          padding: Theme.spacing.s,
          borderRadius: Theme.borderRadius.medium,
          iconSize: 20,
        };
      case 'large':
        return {
          padding: Theme.spacing.m,
          borderRadius: Theme.borderRadius.medium,
          iconSize: 24,
        };
      default:
        return {
          padding: Theme.spacing.s,
          borderRadius: Theme.borderRadius.medium,
          iconSize: 20,
        };
    }
  };

  const buttonStyles = getButtonStyles();
  const iconColor = getIconColor();
  const { padding, borderRadius, iconSize } = getButtonSize();

  return (
    <TouchableOpacity
      onPress={onPress}
      disabled={disabled || loading}
      style={[styles.button, buttonStyles, { padding, borderRadius }, style]}
      activeOpacity={0.7}
      accessibilityLabel={accessibilityLabel || `${String(icon)} button`}
      accessibilityRole="button"
    >
      <View style={styles.content}>
        {loading ? (
          <ActivityIndicator size="small" />
        ) : (
          <Feather name={icon} size={iconSize} />
        )}
      </View>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  button: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    alignItems: 'center',
    justifyContent: 'center',
  },
});
