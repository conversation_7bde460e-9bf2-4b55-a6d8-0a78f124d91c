import React from 'react';
import {
  TouchableOpacity,
  StyleSheet,
  StyleProp,
  ViewStyle,
  TextStyle,
} from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  Easing,
} from 'react-native-reanimated';

import { colors } from '../../constants/Colors';
import Theme from '../../constants/Theme';

import { Text } from './Text';
import { View } from './View';
import { SoulCategory } from './CategoryTab';

export interface RadioButtonProps {
  value: string;
  label: string;
  isSelected: boolean;
  onSelect: (value: string) => void;
  category?: SoulCategory;
  disabled?: boolean;
  style?: StyleProp<ViewStyle>;
  labelStyle?: StyleProp<TextStyle>;
  accessible?: boolean;
  accessibilityLabel?: string;
}

// Map category to color key in Colors
const categoryColorMap: Record<SoulCategory, keyof ColorTheme> = {
  jism: 'jismRed',
  nafs: 'nafsOrange',
  aql: 'aqlYellow',
  qalb: 'qalbGreen',
  ruh: 'spiritualBlue',
};

export function RadioButton({
  value,
  label,
  isSelected,
  onSelect,
  category,
  disabled = false,
  style,
  labelStyle,
  accessible = true,
  accessibilityLabel,
}: RadioButtonProps) {
  // Animation values
  const selectionScale = useSharedValue(isSelected ? 1 : 0);
  const buttonScale = useSharedValue(1);

  // Get the appropriate color based on category or use primary color
  const accentColor = category
    ? colors[categoryColorMap[category]]
    : colors.primary;

  // Update animation when selection state changes
  React.useEffect(() => {
    selectionScale.value = withTiming(isSelected ? 1 : 0, {
      duration: Theme.animation.duration.normal,
      easing: Easing.bezier(0.25, 0.1, 0.25, 1),
    });
  }, [isSelected, selectionScale]);

  // Animation for press feedback
  const handlePressIn = () => {
    if (!disabled) {
      buttonScale.value = withTiming(0.95, {
        duration: Theme.animation.duration.short,
        easing: Easing.bezier(0.25, 0.1, 0.25, 1),
      });
    }
  };

  const handlePressOut = () => {
    if (!disabled) {
      buttonScale.value = withTiming(1, {
        duration: Theme.animation.duration.short,
        easing: Easing.bezier(0.25, 0.1, 0.25, 1),
      });
    }
  };

  // Animated styles
  const outerCircleStyle = useAnimatedStyle(() => {
    return {
      transform: [{ scale: buttonScale.value }],
      borderColor: isSelected ? accentColor : colors.border,
      opacity: disabled ? 0.5 : 1,
    };
  });

  const innerCircleStyle = useAnimatedStyle(() => {
    return {
      transform: [{ scale: selectionScale.value }],
      backgroundColor: accentColor,
    };
  });

  return (
    <TouchableOpacity
      style={[styles.container, style]}
      onPress={() => !disabled && onSelect(value)}
      onPressIn={handlePressIn}
      onPressOut={handlePressOut}
      disabled={disabled}
      activeOpacity={0.7}
      accessible={accessible}
      accessibilityLabel={
        accessibilityLabel ||
        `${label}, ${isSelected ? 'selected' : 'not selected'}`
      }
      accessibilityRole="radio"
      accessibilityState={{
        selected: isSelected,
        disabled: disabled,
      }}
    >
      <Animated.View style={[styles.outerCircle, outerCircleStyle]}>
        <Animated.View style={[styles.innerCircle, innerCircleStyle]} />
      </Animated.View>

      <Text
        variant="body"
        style={[
          styles.label,
          { color: disabled ? colors.textSecondary : colors.text },
          labelStyle,
        ]}
      >
        {label}
      </Text>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: Theme.spacing.xs,
    marginVertical: Theme.spacing.xs,
  },
  outerCircle: {
    width: 24,
    height: 24,
    borderRadius: 12,
    borderWidth: 2,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'transparent',
    ...Theme.shadows.small,
  },
  innerCircle: {
    width: 12,
    height: 12,
    borderRadius: 6,
  },
  label: {
    marginLeft: Theme.spacing.s,
    flex: 1,
  },
});
