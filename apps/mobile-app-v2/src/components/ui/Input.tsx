import React, { useState } from 'react';

import {
  TextInput,
  StyleSheet,
  StyleProp,
  ViewStyle,
  TextStyle,
  View as RNView,
  TouchableOpacity,
} from 'react-native';

import { Feather } from '@expo/vector-icons';

import { colors } from "../../constants/Colors";
import Theme from '../../constants/Theme';

import { Text } from './Text';
import { View } from './View';

export type InputProps = React.ComponentProps<typeof TextInput> & {
  label?: string;
  error?: string;
  helper?: string;
  leftIcon?: keyof typeof Feather.glyphMap;
  rightIcon?: keyof typeof Feather.glyphMap;
  onRightIconPress?: () => void;
  containerStyle?: StyleProp<ViewStyle>;
  inputStyle?: StyleProp<TextStyle>;
  labelStyle?: StyleProp<TextStyle>;
  helperStyle?: StyleProp<TextStyle>;
  errorStyle?: StyleProp<TextStyle>;
};

export function Input({
  label,
  error,
  helper,
  leftIcon,
  rightIcon,
  onRightIconPress,
  containerStyle,
  inputStyle,
  labelStyle,
  helperStyle,
  errorStyle,
  secureTextEntry,
  ...rest
}: InputProps) {
  const [isFocused, setIsFocused] = useState(false);
  const [isPasswordVisible, setIsPasswordVisible] = useState(!secureTextEntry);

  const handleFocus = (e: any) => {
    setIsFocused(true);
    if (rest.onFocus) {
      rest.onFocus(e);
    }
  };

  const handleBlur = (e: any) => {
    setIsFocused(false);
    if (rest.onBlur) {
      rest.onBlur(e);
    }
  };

  const togglePasswordVisibility = () => {
    setIsPasswordVisible(!isPasswordVisible);
  };

  // Determine border color based on state
  const getBorderColor = () => {
    if (error) {
      return colors.error;
    }
    if (isFocused) {
      return colors.primary;
    }
    return colors.border;
  };

  // If secureTextEntry is defined, override rightIcon with eye/eye-off
  const passwordIcon = isPasswordVisible ? 'eye-off' : 'eye';
  const actualRightIcon =
    secureTextEntry !== undefined ? passwordIcon : rightIcon;
  const actualOnRightIconPress =
    secureTextEntry !== undefined ? togglePasswordVisibility : onRightIconPress;

  return (
    <View style={[styles.container, containerStyle]}>
      {label && (
        <Text variant="caption" style={[styles.label, labelStyle]}>
          {label}
        </Text>
      )}

      <RNView
        style={[
          styles.inputContainer,
          {
            borderColor: getBorderColor(),
            backgroundColor: colors.surface,
          },
        ]}
      >
        {leftIcon && (
          <Feather name={leftIcon} size={20} style={styles.leftIcon} />
        )}

        <TextInput
          style={[
            styles.input,
            {
              color: colors.text,
              paddingLeft: leftIcon ? 0 : Theme.spacing.m,
              paddingRight: actualRightIcon ? 0 : Theme.spacing.m,
            },
            inputStyle,
          ]}
          placeholderTextColor={colors.textSecondary}
          onFocus={handleFocus}
          onBlur={handleBlur}
          secureTextEntry={secureTextEntry && !isPasswordVisible}
          {...rest}
        />

        {actualRightIcon && (
          <TouchableOpacity
            onPress={actualOnRightIconPress}
            style={styles.rightIcon}
            disabled={!actualOnRightIconPress}
          >
            <Feather name={actualRightIcon} size={20} />
          </TouchableOpacity>
        )}
      </RNView>

      {(error || helper) && (
        <Text
          variant="caption"
          style={[styles.helperText, error ? errorStyle : helperStyle]}
        >
          {error || helper}
        </Text>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    width: '100%',
    marginBottom: Theme.spacing.m,
  },
  label: {
    marginBottom: Theme.spacing.xs,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderRadius: Theme.borderRadius.medium,
    overflow: 'hidden',
  },
  input: {
    flex: 1,
    height: 48,
    paddingVertical: Theme.spacing.s,
    fontFamily: Theme.typography.fontFamily.regular,
    fontSize: Theme.typography.fontSize.m,
  },
  leftIcon: {
    paddingLeft: Theme.spacing.m,
    paddingRight: Theme.spacing.xs,
  },
  rightIcon: {
    paddingRight: Theme.spacing.m,
    paddingLeft: Theme.spacing.xs,
  },
  helperText: {
    marginTop: Theme.spacing.xs,
  },
});
