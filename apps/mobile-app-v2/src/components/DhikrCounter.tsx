import React, { useEffect, useRef, useState } from 'react';

import {
  Animated,
  Pressable,
  StyleSheet,
  TouchableOpacity,
  Vibration,
} from 'react-native';

import { Feather } from '@expo/vector-icons';
import * as Haptics from 'expo-haptics';
import { LinearGradient } from 'expo-linear-gradient';

import { colors } from '../constants/Colors';
import Theme from '../constants/Theme';

import { Text } from './ui/Text';
import { View } from './ui/View';

interface DhikrCounterProps {
  arabic: string;
  transliteration: string;
  translation: string;
  targetCount: number;
  onComplete: (count: number) => void;
}

export const DhikrCounter = ({
  arabic,
  transliteration,
  translation,
  targetCount,
  onComplete,
}: DhikrCounterProps) => {
  // State
  const [count, setCount] = useState(0);
  const [showCompletion, setShowCompletion] = useState(false);

  // Animation values
  const scaleAnim = useRef(new Animated.Value(1)).current;
  const rotateAnim = useRef(new Animated.Value(0)).current;

  // Calculate completion percentage
  const completionPercentage = Math.min(
    100,
    Math.round((count / targetCount) * 100)
  );

  // Trigger completion when target is reached
  useEffect(() => {
    if (count >= targetCount && !showCompletion) {
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
      setShowCompletion(true);

      // Delay before calling onComplete to allow animation to play
      setTimeout(() => {
        onComplete(count);
      }, 2000);
    }
  }, [count, targetCount, onComplete, showCompletion]);

  // Handle counter press
  const handlePress = () => {
    if (showCompletion) {
      return;
    }

    // Increment count
    setCount((prev) => prev + 1);

    // Haptic feedback
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);

    // Add vibration on milestones
    if ((count + 1) % 10 === 0) {
      Vibration.vibrate(50);
    }

    if ((count + 1) % targetCount === 0) {
      Vibration.vibrate([0, 50, 50, 100]);
    }

    // Trigger scale animation
    Animated.sequence([
      Animated.timing(scaleAnim, {
        toValue: 0.95,
        duration: 100,
        useNativeDriver: true,
      }),
      Animated.timing(scaleAnim, {
        toValue: 1,
        duration: 100,
        useNativeDriver: true,
      }),
    ]).start();

    // Trigger rotation animation
    Animated.timing(rotateAnim, {
      toValue: count + 1,
      duration: 300,
      useNativeDriver: true,
    }).start();
  };

  // Reset counter
  const handleReset = () => {
    if (showCompletion) {
      return;
    }

    // Haptic feedback
    Haptics.notificationAsync(Haptics.NotificationFeedbackType.Warning);

    // Reset count
    setCount(0);

    // Reset animations
    rotateAnim.setValue(0);
  };

  // Calculate rotation for animation
  const rotate = rotateAnim.interpolate({
    inputRange: [0, targetCount],
    outputRange: ['0deg', '360deg'],
  });

  return (
    <View style={styles.container}>
      <View style={styles.dhikrTextContainer}>
        <Text variant="heading2" style={styles.arabicText}>
          {arabic}
        </Text>
        <Text variant="subtitle" style={styles.transliterationText}>
          {transliteration}
        </Text>
        <Text
          variant="body"
          color="textSecondary"
          style={styles.translationText}
        >
          {translation}
        </Text>
      </View>

      <View style={styles.counterContainer}>
        <Pressable onPress={handleReset} style={styles.resetButton}></Pressable>

        <TouchableOpacity
          activeOpacity={0.8}
          onPress={handlePress}
          disabled={showCompletion}
        >
          <Animated.View
            style={[
              styles.counterButton,
              {
                transform: [{ scale: scaleAnim }, { rotate }],
              },
            ]}
          >
            <LinearGradient
              colors={[colors.primary, colors.accent]}
              style={styles.gradient}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
            />
            <View style={styles.countTextContainer}>
              <Text variant="heading1" color="surface" style={styles.countText}>
                {count}
              </Text>
              <Text variant="caption" color="surface">
                / {targetCount}
              </Text>
            </View>
          </Animated.View>
        </TouchableOpacity>
      </View>

      <View style={styles.progressContainer}>
        <View style={[styles.progressBar, { backgroundColor: colors.border }]}>
          <View
            style={[
              styles.progressFill,
              {
                width: `${completionPercentage}%`,
                backgroundColor: showCompletion
                  ? colors.success
                  : colors.primary,
              },
            ]}
          />
        </View>
        <Text variant="caption" color="textSecondary">
          {completionPercentage}% Complete
        </Text>
      </View>

      {showCompletion && (
        <View style={styles.completionContainer}>
          <Feather
            name="check-circle"
            size={24}
            style={styles.completionIcon}
          />
          <Text variant="subtitle" color="success">
            Dhikr Complete!
          </Text>
        </View>
      )}

      <View style={styles.instructionContainer}>
        <Text
          variant="caption"
          color="textSecondary"
          style={styles.instructionText}
        >
          Tap the circle to count your dhikr. Press the refresh icon to reset.
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    padding: Theme.spacing.m,
  },
  dhikrTextContainer: {
    alignItems: 'center',
    marginBottom: Theme.spacing.l,
  },
  arabicText: {
    textAlign: 'center',
    marginBottom: Theme.spacing.s,
    fontWeight: 'bold',
  },
  transliterationText: {
    textAlign: 'center',
    marginBottom: Theme.spacing.xs,
  },
  translationText: {
    textAlign: 'center',
    fontStyle: 'italic',
  },
  counterContainer: {
    position: 'relative',
    marginBottom: Theme.spacing.l,
  },
  resetButton: {
    position: 'absolute',
    top: -10,
    right: -10,
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
    borderRadius: 20,
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1,
    ...Theme.shadows.small,
  },
  counterButton: {
    width: 150,
    height: 150,
    borderRadius: 75,
    justifyContent: 'center',
    alignItems: 'center',
    overflow: 'hidden',
    ...Theme.shadows.medium,
  },
  gradient: {
    position: 'absolute',
    width: '100%',
    height: '100%',
  },
  countTextContainer: {
    alignItems: 'center',
  },
  countText: {
    fontSize: 48,
    fontWeight: 'bold',
  },
  progressContainer: {
    width: '100%',
    alignItems: 'center',
    marginBottom: Theme.spacing.m,
  },
  progressBar: {
    width: '100%',
    height: 8,
    borderRadius: 4,
    marginBottom: Theme.spacing.xs,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    borderRadius: 4,
  },
  completionContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Theme.spacing.m,
    padding: Theme.spacing.s,
    backgroundColor: 'rgba(76, 175, 80, 0.1)',
    borderRadius: Theme.borderRadius.small,
  },
  completionIcon: {
    marginRight: Theme.spacing.xs,
  },
  instructionContainer: {
    width: '100%',
    alignItems: 'center',
  },
  instructionText: {
    textAlign: 'center',
  },
});
