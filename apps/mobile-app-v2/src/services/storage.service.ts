/**
 * Storage Service
 * Handles local data storage and caching for offline functionality
 */

import AsyncStorage from '@react-native-async-storage/async-storage';

export interface StorageItem {
  key: string;
  value: any;
  timestamp: number;
  expiresAt?: number;
}

class StorageService {
  private readonly PREFIX = 'qalb_healing_';

  /**
   * Store data with optional expiration
   */
  async setItem(
    key: string,
    value: any,
    expirationMinutes?: number
  ): Promise<void> {
    try {
      const item: StorageItem = {
        key,
        value,
        timestamp: Date.now(),
        expiresAt: expirationMinutes
          ? Date.now() + expirationMinutes * 60 * 1000
          : undefined,
      };

      await AsyncStorage.setItem(this.getKey(key), JSON.stringify(item));
    } catch (error) {
      console.error('Storage setItem error:', error);
      throw error;
    }
  }

  /**
   * Get data from storage
   */
  async getItem<T>(key: string): Promise<T | null> {
    try {
      const storedData = await AsyncStorage.getItem(this.getKey(key));

      if (!storedData) {
        return null;
      }

      const item: StorageItem = JSON.parse(storedData);

      // Check if item has expired
      if (item.expiresAt && Date.now() > item.expiresAt) {
        await this.removeItem(key);
        return null;
      }

      return item.value as T;
    } catch (error) {
      console.error('Storage getItem error:', error);
      return null;
    }
  }

  /**
   * Remove item from storage
   */
  async removeItem(key: string): Promise<void> {
    try {
      await AsyncStorage.removeItem(this.getKey(key));
    } catch (error) {
      console.error('Storage removeItem error:', error);
      throw error;
    }
  }

  /**
   * Check if item exists and is not expired
   */
  async hasItem(key: string): Promise<boolean> {
    try {
      const item = await this.getItem(key);
      return item !== null;
    } catch (error) {
      console.error('Storage hasItem error:', error);
      return false;
    }
  }

  /**
   * Clear all app data
   */
  async clear(): Promise<void> {
    try {
      const keys = await AsyncStorage.getAllKeys();
      const appKeys = keys.filter((key) => key.startsWith(this.PREFIX));
      await AsyncStorage.multiRemove(appKeys);
    } catch (error) {
      console.error('Storage clear error:', error);
      throw error;
    }
  }

  /**
   * Get all keys with app prefix
   */
  async getAllKeys(): Promise<string[]> {
    try {
      const keys = await AsyncStorage.getAllKeys();
      return keys
        .filter((key) => key.startsWith(this.PREFIX))
        .map((key) => key.replace(this.PREFIX, ''));
    } catch (error) {
      console.error('Storage getAllKeys error:', error);
      return [];
    }
  }

  /**
   * Store multiple items
   */
  async setMultiple(
    items: Array<{ key: string; value: any; expirationMinutes?: number }>
  ): Promise<void> {
    try {
      const promises = items.map((item) =>
        this.setItem(item.key, item.value, item.expirationMinutes)
      );
      await Promise.all(promises);
    } catch (error) {
      console.error('Storage setMultiple error:', error);
      throw error;
    }
  }

  /**
   * Get multiple items
   */
  async getMultiple<T>(keys: string[]): Promise<Record<string, T | null>> {
    try {
      const promises = keys.map(async (key) => ({
        key,
        value: await this.getItem<T>(key),
      }));

      const results = await Promise.all(promises);

      return results.reduce((acc, { key, value }) => {
        acc[key] = value;
        return acc;
      }, {} as Record<string, T | null>);
    } catch (error) {
      console.error('Storage getMultiple error:', error);
      return {};
    }
  }

  /**
   * Cache API response with automatic expiration
   */
  async cacheApiResponse(
    endpoint: string,
    data: any,
    expirationMinutes: number = 60
  ): Promise<void> {
    const cacheKey = `api_cache_${endpoint.replace(/[^a-zA-Z0-9]/g, '_')}`;
    await this.setItem(cacheKey, data, expirationMinutes);
  }

  /**
   * Get cached API response
   */
  async getCachedApiResponse<T>(endpoint: string): Promise<T | null> {
    const cacheKey = `api_cache_${endpoint.replace(/[^a-zA-Z0-9]/g, '_')}`;
    return await this.getItem<T>(cacheKey);
  }

  /**
   * Store user session data
   */
  async storeUserSession(sessionData: any): Promise<void> {
    await this.setItem('user_session', sessionData);
  }

  /**
   * Get user session data
   */
  async getUserSession<T>(): Promise<T | null> {
    return await this.getItem<T>('user_session');
  }

  /**
   * Clear user session
   */
  async clearUserSession(): Promise<void> {
    await this.removeItem('user_session');
  }

  /**
   * Store offline data for sync later
   */
  async storeOfflineData(type: string, data: any): Promise<void> {
    const offlineKey = `offline_${type}_${Date.now()}`;
    await this.setItem(offlineKey, {
      type,
      data,
      timestamp: Date.now(),
    });
  }

  /**
   * Get all offline data for syncing
   */
  async getOfflineData(): Promise<
    Array<{ key: string; type: string; data: any; timestamp: number }>
  > {
    try {
      const keys = await this.getAllKeys();
      const offlineKeys = keys.filter((key) => key.startsWith('offline_'));

      const offlineData = await this.getMultiple(offlineKeys);

      return Object.entries(offlineData)
        .filter(([_, value]) => value !== null)
        .map(([key, value]) => ({
          key,
          ...(value as any),
        }));
    } catch (error) {
      console.error('Storage getOfflineData error:', error);
      return [];
    }
  }

  /**
   * Clear synced offline data
   */
  async clearOfflineData(keys: string[]): Promise<void> {
    try {
      const promises = keys.map((key) => this.removeItem(key));
      await Promise.all(promises);
    } catch (error) {
      console.error('Storage clearOfflineData error:', error);
      throw error;
    }
  }

  /**
   * Get storage usage statistics
   */
  async getStorageStats(): Promise<{
    totalKeys: number;
    cacheKeys: number;
    offlineKeys: number;
    sessionKeys: number;
  }> {
    try {
      const keys = await this.getAllKeys();

      return {
        totalKeys: keys.length,
        cacheKeys: keys.filter((key) => key.startsWith('api_cache_')).length,
        offlineKeys: keys.filter((key) => key.startsWith('offline_')).length,
        sessionKeys: keys.filter((key) => key.includes('session')).length,
      };
    } catch (error) {
      console.error('Storage getStorageStats error:', error);
      return {
        totalKeys: 0,
        cacheKeys: 0,
        offlineKeys: 0,
        sessionKeys: 0,
      };
    }
  }

  /**
   * Clean up expired items
   */
  async cleanupExpiredItems(): Promise<number> {
    try {
      const keys = await this.getAllKeys();
      let cleanedCount = 0;

      for (const key of keys) {
        const item = await this.getItem(key);
        if (item === null) {
          cleanedCount++;
        }
      }

      return cleanedCount;
    } catch (error) {
      console.error('Storage cleanup error:', error);
      return 0;
    }
  }

  /**
   * Get prefixed key
   */
  private getKey(key: string): string {
    return `${this.PREFIX}${key}`;
  }

  /**
   * Store user data
   */
  async storeUserData(userData: any): Promise<void> {
    try {
      await AsyncStorage.setItem(
        this.getKey('user_data'),
        JSON.stringify(userData)
      );
    } catch (error) {
      console.error('Failed to store user data:', error);
      throw error;
    }
  }

  /**
   * Get user data
   */
  async getUserData<T>(): Promise<T | null> {
    try {
      const dataString = await AsyncStorage.getItem(this.getKey('user_data'));
      if (!dataString) {
        return null;
      }
      return JSON.parse(dataString);
    } catch (error) {
      console.error('Failed to get user data:', error);
      return null;
    }
  }

  /**
   * Store offline data with automatic key generation
   */
  async storeOfflineData(data: any): Promise<void> {
    try {
      await AsyncStorage.setItem(
        this.getKey('offline_data'),
        JSON.stringify(data)
      );
    } catch (error) {
      console.error('Failed to store offline data:', error);
      throw error;
    }
  }

  /**
   * Sync offline data when connection restored
   */
  async syncOfflineData(): Promise<boolean> {
    try {
      const offlineDataString = await AsyncStorage.getItem(
        this.getKey('offline_data')
      );
      if (!offlineDataString) {
        return true;
      }

      const offlineData = JSON.parse(offlineDataString);

      // Simulate sync to server
      // In real implementation, this would send data to the server
      console.log('Syncing offline data:', offlineData);

      // Remove offline data after successful sync
      await AsyncStorage.removeItem(this.getKey('offline_data'));

      return true;
    } catch (error) {
      console.error('Failed to sync offline data:', error);
      return false;
    }
  }

  /**
   * Clear all data on logout
   */
  async clearAllData(): Promise<void> {
    try {
      await AsyncStorage.clear();
    } catch (error) {
      console.error('Failed to clear all data:', error);
      throw error;
    }
  }
}

// Export singleton instance
export const storageService = new StorageService();
export default storageService;
