/**
 * Data Source Provider for managing API vs Mock data
 * Allows switching between real API and mock data for development
 */

export interface DataSourceConfig {
  useRealApi: boolean;
  apiBaseUrl?: string;
  mockDataEnabled: boolean;
}

class DataSourceProvider {
  private config: DataSourceConfig = {
    useRealApi: false, // Default to mock data for development
    apiBaseUrl: process.env.EXPO_PUBLIC_API_URL || 'http://localhost:3000/api',
    mockDataEnabled: true,
  };

  private listeners: Array<(config: DataSourceConfig) => void> = [];

  /**
   * Get current data source configuration
   */
  getConfig(): DataSourceConfig {
    return { ...this.config };
  }

  /**
   * Check if currently using real API
   */
  isUsingRealApi(): boolean {
    return this.config.useRealApi;
  }

  /**
   * Check if mock data is enabled
   */
  isMockDataEnabled(): boolean {
    return this.config.mockDataEnabled;
  }

  /**
   * Switch to real API
   */
  useRealApi(apiBaseUrl?: string) {
    this.config = {
      ...this.config,
      useRealApi: true,
      apiBaseUrl: apiBaseUrl || this.config.apiBaseUrl,
    };
    this.notifyListeners();
  }

  /**
   * Switch to mock data
   */
  useMockData() {
    this.config = {
      ...this.config,
      useRealApi: false,
      mockDataEnabled: true,
    };
    this.notifyListeners();
  }

  /**
   * Toggle between real API and mock data
   */
  toggleDataSource() {
    if (this.config.useRealApi) {
      this.useMockData();
    } else {
      this.useRealApi();
    }
  }

  /**
   * Set API base URL
   */
  setApiBaseUrl(url: string) {
    this.config = {
      ...this.config,
      apiBaseUrl: url,
    };
    this.notifyListeners();
  }

  /**
   * Subscribe to data source changes
   */
  subscribe(listener: (config: DataSourceConfig) => void) {
    this.listeners.push(listener);
    
    // Return unsubscribe function
    return () => {
      const index = this.listeners.indexOf(listener);
      if (index > -1) {
        this.listeners.splice(index, 1);
      }
    };
  }

  /**
   * Notify all listeners of config changes
   */
  private notifyListeners() {
    this.listeners.forEach(listener => listener(this.config));
  }

  /**
   * Get appropriate API endpoint
   */
  getApiEndpoint(path: string): string {
    if (!this.config.useRealApi) {
      throw new Error('Cannot get API endpoint when using mock data');
    }
    
    const baseUrl = this.config.apiBaseUrl?.replace(/\/$/, '') || '';
    const cleanPath = path.startsWith('/') ? path : `/${path}`;
    
    return `${baseUrl}${cleanPath}`;
  }

  /**
   * Check if API is available
   */
  async checkApiAvailability(): Promise<boolean> {
    if (!this.config.apiBaseUrl) {
      return false;
    }

    try {
      const response = await fetch(`${this.config.apiBaseUrl}/health`, {
        method: 'GET',
        timeout: 5000,
      } as any);
      
      return response.ok;
    } catch (error) {
      console.warn('API availability check failed:', error);
      return false;
    }
  }

  /**
   * Auto-detect and configure data source based on API availability
   */
  async autoConfigureDataSource(): Promise<void> {
    const isApiAvailable = await this.checkApiAvailability();
    
    if (isApiAvailable) {
      this.useRealApi();
      console.log('✅ Real API detected and configured');
    } else {
      this.useMockData();
      console.log('📱 Using mock data (API not available)');
    }
  }

  /**
   * Get data source status for UI display
   */
  getStatusInfo() {
    return {
      isUsingRealApi: this.config.useRealApi,
      apiBaseUrl: this.config.apiBaseUrl,
      status: this.config.useRealApi ? 'Connected to API' : 'Using Mock Data',
      statusColor: this.config.useRealApi ? '#4CAF50' : '#FF9800',
    };
  }
}

// Export singleton instance
const dataSourceProvider = new DataSourceProvider();

// Auto-configure on app start (non-blocking)
dataSourceProvider.autoConfigureDataSource().catch(console.warn);

export default dataSourceProvider;
