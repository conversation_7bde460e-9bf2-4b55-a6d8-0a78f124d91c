import AsyncStorage from '@react-native-async-storage/async-storage';

// Storage keys
const STORAGE_KEYS = {
  AUTH_TOKEN: '@QalbHealing:auth_token',
  AUTH_REFRESH_TOKEN: '@QalbHealing:auth_refresh_token',
  AUTH_EXPIRY: '@QalbHealing:auth_expiry',
  USER_PROFILE: '@QalbHealing:user_profile',
  DATA_SOURCE: '@QalbHealing:data_source', // 'api' or 'dummy'
  DARK_MODE: '@QalbHealing:dark_mode',
  JOURNEY_PROGRESS: '@QalbHealing:journey_progress',
  EMERGENCY_SESSIONS: '@QalbHealing:emergency_sessions',
  JOURNAL_ENTRIES: '@QalbHealing:journal_entries',
};

/**
 * Get the stored authentication token
 */
export const getStoredToken = async (): Promise<string | null> => {
  try {
    return await AsyncStorage.getItem(STORAGE_KEYS.AUTH_TOKEN);
  } catch (error) {
    console.error('Error retrieving auth token:', error);
    return null;
  }
};

/**
 * Store authentication tokens
 */
export const storeAuthTokens = async (
  token: string,
  refreshToken: string,
  expiryDate: string
): Promise<void> => {
  try {
    await AsyncStorage.multiSet([
      [STORAGE_KEYS.AUTH_TOKEN, token],
      [STORAGE_KEYS.AUTH_REFRESH_TOKEN, refreshToken],
      [STORAGE_KEYS.AUTH_EXPIRY, expiryDate],
    ]);
  } catch (error) {
    console.error('Error storing auth tokens:', error);
  }
};

/**
 * Clear authentication tokens (logout)
 */
export const clearAuthTokens = async (): Promise<void> => {
  try {
    await AsyncStorage.multiRemove([
      STORAGE_KEYS.AUTH_TOKEN,
      STORAGE_KEYS.AUTH_REFRESH_TOKEN,
      STORAGE_KEYS.AUTH_EXPIRY,
    ]);
  } catch (error) {
    console.error('Error clearing auth tokens:', error);
  }
};

/**
 * Store user profile
 */
export const storeUserProfile = async (profile: any): Promise<void> => {
  try {
    await AsyncStorage.setItem(STORAGE_KEYS.USER_PROFILE, JSON.stringify(profile));
  } catch (error) {
    console.error('Error storing user profile:', error);
  }
};

/**
 * Get stored user profile
 */
export const getStoredUserProfile = async (): Promise<any | null> => {
  try {
    const profileJson = await AsyncStorage.getItem(STORAGE_KEYS.USER_PROFILE);
    return profileJson ? JSON.parse(profileJson) : null;
  } catch (error) {
    console.error('Error retrieving user profile:', error);
    return null;
  }
};

/**
 * Store data source preference ('api' or 'dummy')
 */
export const storeDataSource = async (source: 'api' | 'dummy'): Promise<void> => {
  try {
    await AsyncStorage.setItem(STORAGE_KEYS.DATA_SOURCE, source);
  } catch (error) {
    console.error('Error storing data source preference:', error);
  }
};

/**
 * Get stored data source preference
 */
export const getStoredDataSource = async (): Promise<'api' | 'dummy'> => {
  try {
    const source = await AsyncStorage.getItem(STORAGE_KEYS.DATA_SOURCE);
    return (source as 'api' | 'dummy') || 'dummy'; // Default to dummy data
  } catch (error) {
    console.error('Error retrieving data source preference:', error);
    return 'dummy'; // Default to dummy data on error
  }
};

/**
 * Store dark mode preference
 */
export const storeDarkMode = async (isDarkMode: boolean): Promise<void> => {
  try {
    await AsyncStorage.setItem(STORAGE_KEYS.DARK_MODE, String(isDarkMode));
  } catch (error) {
    console.error('Error storing dark mode preference:', error);
  }
};

/**
 * Get stored dark mode preference
 */
export const getStoredDarkMode = async (): Promise<boolean | null> => {
  try {
    const isDarkMode = await AsyncStorage.getItem(STORAGE_KEYS.DARK_MODE);
    return isDarkMode ? isDarkMode === 'true' : null;
  } catch (error) {
    console.error('Error retrieving dark mode preference:', error);
    return null;
  }
};

/**
 * Store journey progress
 */
export const storeJourneyProgress = async (progress: any): Promise<void> => {
  try {
    await AsyncStorage.setItem(STORAGE_KEYS.JOURNEY_PROGRESS, JSON.stringify(progress));
  } catch (error) {
    console.error('Error storing journey progress:', error);
  }
};

/**
 * Get stored journey progress
 */
export const getStoredJourneyProgress = async (): Promise<any | null> => {
  try {
    const progressJson = await AsyncStorage.getItem(STORAGE_KEYS.JOURNEY_PROGRESS);
    return progressJson ? JSON.parse(progressJson) : null;
  } catch (error) {
    console.error('Error retrieving journey progress:', error);
    return null;
  }
};

/**
 * Store emergency sessions locally
 */
export const storeEmergencySessions = async (sessions: any[]): Promise<void> => {
  try {
    await AsyncStorage.setItem(STORAGE_KEYS.EMERGENCY_SESSIONS, JSON.stringify(sessions));
  } catch (error) {
    console.error('Error storing emergency sessions:', error);
  }
};

/**
 * Get stored emergency sessions
 */
export const getStoredEmergencySessions = async (): Promise<any[] | null> => {
  try {
    const sessionsJson = await AsyncStorage.getItem(STORAGE_KEYS.EMERGENCY_SESSIONS);
    return sessionsJson ? JSON.parse(sessionsJson) : null;
  } catch (error) {
    console.error('Error retrieving emergency sessions:', error);
    return null;
  }
};

/**
 * Store journal entries locally
 */
export const storeJournalEntries = async (entries: any[]): Promise<void> => {
  try {
    await AsyncStorage.setItem(STORAGE_KEYS.JOURNAL_ENTRIES, JSON.stringify(entries));
  } catch (error) {
    console.error('Error storing journal entries:', error);
  }
};

/**
 * Get stored journal entries
 */
export const getStoredJournalEntries = async (): Promise<any[] | null> => {
  try {
    const entriesJson = await AsyncStorage.getItem(STORAGE_KEYS.JOURNAL_ENTRIES);
    return entriesJson ? JSON.parse(entriesJson) : null;
  } catch (error) {
    console.error('Error retrieving journal entries:', error);
    return null;
  }
};

/**
 * Clear all stored data (for logout or reset)
 */
export const clearAllStoredData = async (): Promise<void> => {
  try {
    await AsyncStorage.multiRemove(Object.values(STORAGE_KEYS));
  } catch (error) {
    console.error('Error clearing all stored data:', error);
  }
};
