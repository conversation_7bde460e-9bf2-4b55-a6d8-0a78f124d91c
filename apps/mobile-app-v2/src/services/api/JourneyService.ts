import { BaseService, ServiceErrorType, ServiceError } from "./BaseService";
import * as Types from "./types";
import { dummyJourney, dummyJourneyAnalytics } from "../data/dummyData";

/**
 * JourneyService handles all API interactions related to healing journeys
 */
class JourneyService extends BaseService {
  constructor() {
    super("journey");
  }

  /**
   * Start a new healing journey
   * @param journeyData Journey configuration data
   * @returns The created journey
   */
  async startJourney(journeyData: {
    journeyType: Types.JourneyType;
    focusLayers: Types.SoulLayer[];
    customDuration?: number;
  }): Promise<Types.Journey> {
    try {
      return await this.fetchData<Types.Journey>(
        "/journey/start",
        dummy<PERSON><PERSON>ney,
        {
          method: "POST",
          body: journeyData,
          useCache: false,
        }
      );
    } catch (error) {
      if (error instanceof ServiceError) {
        // Handle specific error cases
        if (error.type === ServiceErrorType.VALIDATION) {
          throw new ServiceError(
            "Invalid journey configuration. Please check your inputs.",
            error.type,
            error.statusCode,
            error.details
          );
        }

        throw new ServiceError(
          `Failed to start journey: ${error.message}`,
          error.type,
          error.statusCode,
          error.details
        );
      }

      throw new ServiceError(
        "An unexpected error occurred while starting the journey",
        ServiceErrorType.UNKNOWN
      );
    }
  }

  /**
   * Get all available journeys for the user
   * @returns List of available journeys
   */
  async getJourneys(): Promise<Types.Journey[]> {
    try {
      return await this.fetchData<Types.Journey[]>(
        "/journey/all",
        [dummyJourney], // Return array of journeys
        {
          method: "GET",
          useCache: true,
        }
      );
    } catch (error) {
      if (error instanceof ServiceError) {
        throw new ServiceError(
          `Failed to retrieve journeys: ${error.message}`,
          error.type,
          error.statusCode,
          error.details
        );
      }

      throw new ServiceError(
        "An unexpected error occurred while retrieving journeys",
        ServiceErrorType.UNKNOWN
      );
    }
  }

  /**
   * Get the user's current active journey
   * @returns The current active journey or null if none exists
   */
  async getCurrentJourney(): Promise<Types.Journey | null> {
    try {
      return await this.fetchData<Types.Journey>(
        "/journey/current",
        dummyJourney,
        {
          method: "GET",
          useCache: true,
        }
      );
    } catch (error) {
      if (error instanceof ServiceError) {
        // If no active journey, return null instead of throwing
        if (error.type === ServiceErrorType.NOT_FOUND) {
          return null;
        }

        throw new ServiceError(
          `Failed to retrieve current journey: ${error.message}`,
          error.type,
          error.statusCode,
          error.details
        );
      }

      throw new ServiceError(
        "An unexpected error occurred while retrieving the current journey",
        ServiceErrorType.UNKNOWN
      );
    }
  }

  /**
   * Update the progress of the current journey
   * @param progress Journey progress update data
   * @returns Updated completion and journey details
   */
  async updateProgress(progress: Types.JourneyProgress): Promise<{
    completion: {
      id: string;
      user_id: string;
      module_id: string;
      status: string;
      reflections?: string;
      challenges?: string;
      completion_date: string;
    };
    journey: {
      id: string;
      user_id: string;
      current_day: number;
      last_activity_date: string;
    };
    newAchievements?: string[];
  }> {
    try {
      return await this.fetchData<any>(
        "/journey/progress",
        {
          completion: {
            id: "completion-456",
            user_id: "user-123",
            module_id: progress.moduleId,
            status: progress.status,
            reflections: progress.reflections,
            challenges: progress.challenges,
            completion_date: new Date().toISOString(),
          },
          journey: {
            id: dummyJourney.id,
            user_id: dummyJourney.user_id,
            current_day: dummyJourney.current_day + 1,
            last_activity_date: new Date().toISOString(),
          },
          newAchievements: ["CONSISTENT_SEEKER"],
        },
        {
          method: "PATCH",
          body: progress,
          useCache: false,
        }
      );
    } catch (error) {
      if (error instanceof ServiceError) {
        // Handle specific error cases
        if (error.type === ServiceErrorType.NOT_FOUND) {
          throw new ServiceError(
            "Journey or module not found. Please refresh and try again.",
            error.type,
            error.statusCode
          );
        }

        throw new ServiceError(
          `Failed to update journey progress: ${error.message}`,
          error.type,
          error.statusCode,
          error.details
        );
      }

      throw new ServiceError(
        "An unexpected error occurred while updating journey progress",
        ServiceErrorType.UNKNOWN
      );
    }
  }

  /**
   * Get analytics data for the user's journey
   * @returns Journey analytics
   */
  async getJourneyAnalytics(): Promise<Types.JourneyAnalytics> {
    try {
      return await this.fetchData<Types.JourneyAnalytics>(
        "/journey/analytics",
        dummyJourneyAnalytics,
        {
          method: "GET",
          useCache: true,
        }
      );
    } catch (error) {
      if (error instanceof ServiceError) {
        throw new ServiceError(
          `Failed to retrieve journey analytics: ${error.message}`,
          error.type,
          error.statusCode,
          error.details
        );
      }

      throw new ServiceError(
        "An unexpected error occurred while retrieving journey analytics",
        ServiceErrorType.UNKNOWN
      );
    }
  }

  /**
   * Get the user's earned achievements
   * @returns List of user achievements
   */
  async getAchievements(): Promise<{
    achievements: Types.UserAchievement[];
  }> {
    try {
      return await this.fetchData<{ achievements: Types.UserAchievement[] }>(
        "/journey/achievements",
        {
          achievements: [
            {
              id: "ua-123",
              user_id: "user-123",
              achievement_type: "WEEK_WARRIOR",
              earned_date: "2025-05-27T12:00:00Z",
              achievements: {
                id: "ach-1",
                name: "Week Warrior",
                description: "Completed 7 daily modules",
                icon: "warrior.svg",
              },
            },
            {
              id: "ua-124",
              user_id: "user-123",
              achievement_type: "CONSISTENT_SEEKER",
              earned_date: "2025-05-27T12:00:00Z",
              achievements: {
                id: "ach-2",
                name: "Consistent Seeker",
                description: "Maintained a 10-day streak",
                icon: "seeker.svg",
              },
            },
          ],
        },
        {
          method: "GET",
          useCache: true,
        }
      );
    } catch (error) {
      if (error instanceof ServiceError) {
        throw new ServiceError(
          `Failed to retrieve achievements: ${error.message}`,
          error.type,
          error.statusCode,
          error.details
        );
      }

      throw new ServiceError(
        "An unexpected error occurred while retrieving achievements",
        ServiceErrorType.UNKNOWN
      );
    }
  }

  /**
   * Submit a daily check-in to track progress
   * @param checkIn Daily check-in data
   * @returns The created check-in
   */
  async submitDailyCheckIn(checkIn: Types.DailyCheckIn): Promise<{
    checkIn: {
      id: string;
      user_id: string;
      mood: string;
      dhikr_count: number;
      prayer_consistency: number;
      notes?: string;
      check_in_date: string;
    };
  }> {
    try {
      return await this.fetchData<any>(
        "/journey/check-in",
        {
          checkIn: {
            id: `checkin-${Date.now()}`,
            user_id: "user-123",
            mood: checkIn.mood,
            dhikr_count: checkIn.dhikr_count,
            prayer_consistency: checkIn.prayer_consistency,
            notes: checkIn.notes,
            check_in_date: new Date().toISOString(),
          },
        },
        {
          method: "POST",
          body: checkIn,
          useCache: false,
        }
      );
    } catch (error) {
      if (error instanceof ServiceError) {
        throw new ServiceError(
          `Failed to submit daily check-in: ${error.message}`,
          error.type,
          error.statusCode,
          error.details
        );
      }

      throw new ServiceError(
        "An unexpected error occurred while submitting daily check-in",
        ServiceErrorType.UNKNOWN
      );
    }
  }

  /**
   * Get personalized resources based on the current journey
   * @returns Recommended resources
   */
  async getRecommendedResources(): Promise<{
    recommendations: {
      resources: {
        id: number;
        title: string;
        type: string;
        url: string;
      }[];
    };
  }> {
    try {
      return await this.fetchData<any>(
        "/journey/resources",
        {
          recommendations: {
            resources: [
              {
                id: 1,
                title: "Understanding Qalb",
                type: "article",
                url: "/content/understanding-qalb",
              },
              {
                id: 2,
                title: "Nafs Purification",
                type: "video",
                url: "/content/nafs-purification",
              },
              {
                id: 3,
                title: "Dhikr Meditation",
                type: "audio",
                url: "/content/dhikr-meditation",
              },
            ],
          },
        },
        {
          method: "GET",
          useCache: true,
        }
      );
    } catch (error) {
      if (error instanceof ServiceError) {
        throw new ServiceError(
          `Failed to retrieve recommended resources: ${error.message}`,
          error.type,
          error.statusCode,
          error.details
        );
      }

      throw new ServiceError(
        "An unexpected error occurred while retrieving recommended resources",
        ServiceErrorType.UNKNOWN
      );
    }
  }

  /**
   * Modify an existing journey (reset, extend, or change focus)
   * @param modifyData Journey modification data
   * @returns The modified journey
   */
  async modifyJourney(modifyData: {
    action: "reset" | "extend" | "modify_focus";
    additionalDays?: number;
    newFocusLayers?: Types.SoulLayer[];
  }): Promise<{
    journey: {
      id: string;
      user_id: string;
      duration_days: number;
      status: string;
    };
  }> {
    try {
      return await this.fetchData<any>(
        "/journey/modify",
        {
          journey: {
            id: dummyJourney.id,
            user_id: dummyJourney.user_id,
            duration_days:
              modifyData.action === "extend" && modifyData.additionalDays
                ? dummyJourney.duration_days + modifyData.additionalDays
                : dummyJourney.duration_days,
            status: "active",
          },
        },
        {
          method: "PATCH",
          body: modifyData,
          useCache: false,
        }
      );
    } catch (error) {
      if (error instanceof ServiceError) {
        // Handle specific error cases
        if (error.type === ServiceErrorType.NOT_FOUND) {
          throw new ServiceError(
            "Journey not found. Please refresh and try again.",
            error.type,
            error.statusCode
          );
        }

        if (error.type === ServiceErrorType.VALIDATION) {
          throw new ServiceError(
            "Invalid modification parameters. Please check your inputs.",
            error.type,
            error.statusCode,
            error.details
          );
        }

        throw new ServiceError(
          `Failed to modify journey: ${error.message}`,
          error.type,
          error.statusCode,
          error.details
        );
      }

      throw new ServiceError(
        "An unexpected error occurred while modifying the journey",
        ServiceErrorType.UNKNOWN
      );
    }
  }

  /**
   * Get available journey types and their descriptions
   * @returns Journey type options
   */
  async getJourneyTypes(): Promise<{
    journeyTypes: {
      type: Types.JourneyType;
      title: string;
      description: string;
      durationDays: number;
      focusDescription: string;
    }[];
  }> {
    // This is static data that could be fetched from the API in a real implementation
    return {
      journeyTypes: [
        {
          type: "7-day",
          title: "7-Day Mini-Toolkit",
          description:
            "A quick starter journey to experience rapid relief and establish basic Islamic healing practices.",
          durationDays: 7,
          focusDescription:
            "Quick relief and establishing foundational practices",
        },
        {
          type: "14-day",
          title: "14-Day Core Journey",
          description:
            "A two-week journey for deeper transformation focused on key spiritual healing practices.",
          durationDays: 14,
          focusDescription: "Balancing emotional and spiritual healing",
        },
        {
          type: "21-day",
          title: "21-Day Transformation",
          description:
            "Three weeks of comprehensive healing across all five layers for lasting change.",
          durationDays: 21,
          focusDescription: "Habit formation and deeper spiritual connection",
        },
        {
          type: "40-day",
          title: "40-Day Complete Renewal",
          description:
            "The traditional 40-day spiritual cleansing process for profound transformation.",
          durationDays: 40,
          focusDescription: "Complete renewal across all five layers of being",
        },
        {
          type: "custom",
          title: "Custom Journey",
          description:
            "Design your own journey with your preferred duration and focus areas.",
          durationDays: 0, // To be set by user
          focusDescription: "Personalized healing path",
        },
      ],
    };
  }
}

// Export a singleton instance
const journeyService = new JourneyService();
export default journeyService;
