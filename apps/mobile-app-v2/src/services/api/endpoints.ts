import apiClient from './apiClient';
import * as Types from './types';

/**
 * API Endpoints
 * Organized by feature areas based on the API documentation
 */

// Authentication APIs
export const authApi = {
  signup: (email: string, password: string) =>
    apiClient.post<{ user: Types.User }>('/auth/signup', { email, password }),

  login: (email: string, password: string) =>
    apiClient.post<{ user: Types.User; session: Types.AuthSession }>('/auth/login', { email, password }),

  getProfile: () => 
    apiClient.get<{ profile: Types.UserProfile }>('/auth/profile'),

  updateProfile: (data: Partial<Types.UserProfile>) =>
    apiClient.patch<{ profile: Types.UserProfile }>('/auth/profile', data),
};

// Symptom Analysis APIs
export const symptomsApi = {
  submitSymptoms: (symptoms: Types.SymptomSubmission) =>
    apiClient.post<Types.SymptomSubmissionResponse>('/symptoms/submit', symptoms),

  getSymptomHistory: () =>
    apiClient.get<{ history: any[] }>('/symptoms/history'),

  getLatestDiagnosis: () =>
    apiClient.get<{ diagnosis: Types.Diagnosis }>('/symptoms/latest-diagnosis'),

  trackSymptomProgress: (symptomId: number, intensity: number, notes?: string) =>
    apiClient.post<{ tracking: any }>('/symptoms/track', { symptomId, intensity, notes }),
};

// Journey Management APIs
export const journeyApi = {
  startJourney: (journeyType: Types.JourneyType, focusLayers: Types.SoulLayer[], customDuration?: number) =>
    apiClient.post<{ journey: Types.Journey; todaysModule: Types.JourneyModule }>('/journey/start', {
      journeyType,
      focusLayers,
      customDuration,
    }),

  getCurrentJourney: () =>
    apiClient.get<{ journey: Types.Journey }>('/journey/current'),

  updateJourneyProgress: (progress: Types.JourneyProgress) =>
    apiClient.patch<{ 
      completion: any; 
      journey: Partial<Types.Journey>; 
      newAchievements: string[] 
    }>('/journey/progress', progress),

  getJourneyAnalytics: () =>
    apiClient.get<{ analytics: Types.JourneyAnalytics }>('/journey/analytics'),

  getAchievements: () =>
    apiClient.get<{ achievements: Types.UserAchievement[] }>('/journey/achievements'),

  submitDailyCheckIn: (checkIn: Types.DailyCheckIn) =>
    apiClient.post<{ checkIn: any }>('/journey/check-in', checkIn),

  getRecommendedResources: () =>
    apiClient.get<{ recommendations: { resources: Types.ContentItem[] } }>('/journey/resources'),

  modifyJourney: (action: 'extend' | 'reset' | 'change_focus', additionalDays?: number, newFocusLayers?: Types.SoulLayer[]) =>
    apiClient.patch<{ journey: Partial<Types.Journey> }>('/journey/modify', {
      action,
      additionalDays,
      newFocusLayers,
    }),
};

// Journal APIs
export const journalApi = {
  createEntry: (entry: Omit<Types.JournalEntry, 'id' | 'user_id' | 'created_at' | 'updated_at'>) =>
    apiClient.post<{ entry: Types.JournalEntry }>('/journal/entries', entry),

  getEntries: (params?: {
    entryType?: Types.EntryType;
    mood?: string;
    tags?: string[];
    layers?: Types.SoulLayer[];
    startDate?: string;
    endDate?: string;
    page?: number;
    limit?: number;
    sortBy?: string;
    sortDirection?: 'asc' | 'desc';
  }) =>
    apiClient.get<{ 
      entries: Types.JournalEntry[]; 
      pagination: Types.Pagination 
    }>('/journal/entries', { params }),

  getPrompts: (category?: string, layer?: Types.SoulLayer) =>
    apiClient.get<{ prompts: Types.ReflectionPrompt[] }>('/journal/prompts', {
      params: { category, layer },
    }),

  saveReflection: (promptId: string, content: string, mood?: string, layers?: Types.SoulLayer[]) =>
    apiClient.post<{ entry: Types.JournalEntry }>('/journal/reflections', {
      promptId,
      content,
      mood,
      layers,
    }),

  getAnalytics: (timeframe: '7days' | '30days' | '90days' | 'all' = '30days') =>
    apiClient.get<{ analytics: Types.JournalAnalytics }>('/journal/analytics', {
      params: { timeframe },
    }),
};

// Emergency (Sakina) Mode APIs
export const emergencyApi = {
  startSession: (triggerType: Types.EmergencyTriggerType, currentSymptoms?: string[]) =>
    apiClient.post<{
      sessionId: string;
      breathingExercise: Types.BreathingPattern;
      dhikrContent: Types.DhikrContent;
      ruqyahVerses: Types.RuqyahVerse[];
      duaPrompt: any;
      helplineInfo: any;
    }>('/emergency/start', { triggerType, currentSymptoms }),

  getBreathingExercise: (intensity: 'low' | 'medium' | 'high' = 'medium') =>
    apiClient.get<{ pattern: Types.BreathingPattern }>('/emergency/breathing', {
      params: { intensity },
    }),

  getDhikrContent: () =>
    apiClient.get<{ data: Types.DhikrContent }>('/emergency/dhikr'),

  getRuqyahVerses: () =>
    apiClient.get<{ data: Types.RuqyahVerse[] }>('/emergency/ruqyah'),

  updateSession: (sessionId: string, data: {
    status: 'completed';
    effectivenessRating?: number;
    feedback?: string;
  }) =>
    apiClient.patch<{
      session: Types.EmergencySession;
      insights: any;
      followUpRecommendations: any[];
    }>(`/emergency/sessions/${sessionId}`, data),

  saveToJournal: (sessionId: string, notes?: string, tags?: string[]) =>
    apiClient.post<{ journalEntry: Types.JournalEntry }>(`/emergency/sessions/${sessionId}/save`, {
      notes,
      tags,
    }),
};

// Content APIs
export const contentApi = {
  getFeed: (params?: {
    page?: number;
    limit?: number;
    type?: Types.ContentType;
  }) =>
    apiClient.get<{
      items: Types.ContentItem[];
      pagination: Types.Pagination;
    }>('/content/feed', { params }),

  getByCategory: (categoryId: string, page?: number, limit?: number) =>
    apiClient.get<{
      category: Types.ContentCategory;
      items: Types.ContentItem[];
      pagination: Types.Pagination;
    }>(`/content/category/${categoryId}`, {
      params: { page, limit },
    }),

  getItem: (contentId: string) =>
    apiClient.get<{ item: Types.ContentItem }>(`/content/item/${contentId}`),

  search: (query: string, params?: {
    type?: Types.ContentType;
    tags?: string;
    page?: number;
    limit?: number;
  }) =>
    apiClient.get<{
      query: string;
      items: Types.ContentItem[];
      pagination: Types.Pagination;
    }>('/content/search', {
      params: { q: query, ...params },
    }),

  recordInteraction: (interaction: Types.ContentInteraction) =>
    apiClient.post<{ interaction: any }>('/content/interaction', interaction),
};
