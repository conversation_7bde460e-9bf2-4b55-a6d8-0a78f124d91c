import { BaseService, ServiceErrorType, ServiceError } from './BaseService';
import * as Types from './types';
import { dummyJournalEntries } from '../data/dummyData';

/**
 * JournalService handles all API interactions related to the healing journal
 */
class JournalService extends BaseService {
  constructor() {
    super('journal', {
      // Extended cache for journal data as it's personal and doesn't change often
      cacheTimeout: 12 * 60 * 60 * 1000, // 12 hours
    });
  }

  /**
   * Create a new journal entry
   * @param entry Journal entry data
   * @returns The created journal entry
   */
  async createEntry(entry: {
    title: string;
    content: string;
    entryType: Types.EntryType;
    mood?: string;
    tags?: string[];
    layers?: Types.SoulLayer[];
  }): Promise<Types.JournalEntry> {
    try {
      return await this.fetchData<Types.JournalEntry>(
        '/journal/entries',
        {
          id: `journal-${Date.now()}`,
          title: entry.title,
          content: entry.content,
          entryType: entry.entryType,
          mood: entry.mood,
          tags: entry.tags || [],
          layers: entry.layers || [],
          entryDate: new Date().toISOString(),
        },
        {
          method: 'POST',
          body: entry,
          useCache: false,
        }
      );
    } catch (error) {
      if (error instanceof ServiceError) {
        throw new ServiceError(
          `Failed to create journal entry: ${error.message}`,
          error.type,
          error.statusCode,
          error.details
        );
      }
      
      throw new ServiceError(
        'An unexpected error occurred while creating journal entry',
        ServiceErrorType.UNKNOWN
      );
    }
  }

  /**
   * Get journal entries with optional filtering
   * @param params Filter parameters
   * @returns List of journal entries
   */
  async getEntries(params?: {
    entryType?: Types.EntryType;
    mood?: string;
    tags?: string[];
    layers?: Types.SoulLayer[];
    startDate?: string;
    endDate?: string;
    page?: number;
    limit?: number;
    sortBy?: 'date' | 'mood' | 'type';
    sortDirection?: 'asc' | 'desc';
  }): Promise<{
    entries: Types.JournalEntry[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      pages: number;
    };
  }> {
    try {
      // Construct query string for filtering
      const queryParams = params ? new URLSearchParams() : null;
      
      if (params) {
        if (params.entryType) queryParams?.append('entryType', params.entryType);
        if (params.mood) queryParams?.append('mood', params.mood);
        if (params.tags) params.tags.forEach(tag => queryParams?.append('tags', tag));
        if (params.layers) params.layers.forEach(layer => queryParams?.append('layers', layer));
        if (params.startDate) queryParams?.append('startDate', params.startDate);
        if (params.endDate) queryParams?.append('endDate', params.endDate);
        if (params.page) queryParams?.append('page', params.page.toString());
        if (params.limit) queryParams?.append('limit', params.limit.toString());
        if (params.sortBy) queryParams?.append('sortBy', params.sortBy);
        if (params.sortDirection) queryParams?.append('sortDirection', params.sortDirection);
      }
      
      const queryString = queryParams ? `?${queryParams.toString()}` : '';
      
      return await this.fetchData<{
        entries: Types.JournalEntry[];
        pagination: {
          page: number;
          limit: number;
          total: number;
          pages: number;
        };
      }>(
        `/journal/entries${queryString}`,
        {
          entries: dummyJournalEntries,
          pagination: {
            page: params?.page || 1,
            limit: params?.limit || 10,
            total: dummyJournalEntries.length,
            pages: Math.ceil(dummyJournalEntries.length / (params?.limit || 10)),
          },
        },
        {
          method: 'GET',
          useCache: true,
        }
      );
    } catch (error) {
      if (error instanceof ServiceError) {
        throw new ServiceError(
          `Failed to retrieve journal entries: ${error.message}`,
          error.type,
          error.statusCode,
          error.details
        );
      }
      
      throw new ServiceError(
        'An unexpected error occurred while retrieving journal entries',
        ServiceErrorType.UNKNOWN
      );
    }
  }

  /**
   * Get a specific journal entry by ID
   * @param entryId Journal entry ID
   * @returns The journal entry
   */
  async getEntry(entryId: string): Promise<Types.JournalEntry> {
    try {
      // Find the entry in dummy data
      const dummyEntry = dummyJournalEntries.find(entry => entry.id === entryId);
      
      if (!dummyEntry) {
        throw new ServiceError(
          'Journal entry not found',
          ServiceErrorType.NOT_FOUND
        );
      }
      
      return await this.fetchData<Types.JournalEntry>(
        `/journal/entries/${entryId}`,
        dummyEntry,
        {
          method: 'GET',
          useCache: true,
        }
      );
    } catch (error) {
      if (error instanceof ServiceError) {
        if (error.type === ServiceErrorType.NOT_FOUND) {
          throw new ServiceError(
            'Journal entry not found',
            ServiceErrorType.NOT_FOUND
          );
        }
        
        throw new ServiceError(
          `Failed to retrieve journal entry: ${error.message}`,
          error.type,
          error.statusCode,
          error.details
        );
      }
      
      throw new ServiceError(
        'An unexpected error occurred while retrieving journal entry',
        ServiceErrorType.UNKNOWN
      );
    }
  }

  /**
   * Update a journal entry
   * @param entryId Journal entry ID
   * @param updates Updates to apply
   * @returns The updated journal entry
   */
  async updateEntry(
    entryId: string,
    updates: {
      title?: string;
      content?: string;
      mood?: string;
      tags?: string[];
      layers?: Types.SoulLayer[];
    }
  ): Promise<Types.JournalEntry> {
    try {
      // Find the entry in dummy data
      const dummyEntry = dummyJournalEntries.find(entry => entry.id === entryId);
      
      if (!dummyEntry) {
        throw new ServiceError(
          'Journal entry not found',
          ServiceErrorType.NOT_FOUND
        );
      }
      
      // Apply updates to dummy entry
      const updatedEntry = {
        ...dummyEntry,
        ...updates,
      };
      
      return await this.fetchData<Types.JournalEntry>(
        `/journal/entries/${entryId}`,
        updatedEntry,
        {
          method: 'PATCH',
          body: updates,
          useCache: false,
        }
      );
    } catch (error) {
      if (error instanceof ServiceError) {
        if (error.type === ServiceErrorType.NOT_FOUND) {
          throw new ServiceError(
            'Journal entry not found',
            ServiceErrorType.NOT_FOUND
          );
        }
        
        throw new ServiceError(
          `Failed to update journal entry: ${error.message}`,
          error.type,
          error.statusCode,
          error.details
        );
      }
      
      throw new ServiceError(
        'An unexpected error occurred while updating journal entry',
        ServiceErrorType.UNKNOWN
      );
    }
  }

  /**
   * Delete a journal entry
   * @param entryId Journal entry ID
   * @returns Success status
   */
  async deleteEntry(entryId: string): Promise<{ success: boolean }> {
    try {
      return await this.fetchData<{ success: boolean }>(
        `/journal/entries/${entryId}`,
        { success: true },
        {
          method: 'DELETE',
          useCache: false,
        }
      );
    } catch (error) {
      if (error instanceof ServiceError) {
        if (error.type === ServiceErrorType.NOT_FOUND) {
          throw new ServiceError(
            'Journal entry not found',
            ServiceErrorType.NOT_FOUND
          );
        }
        
        throw new ServiceError(
          `Failed to delete journal entry: ${error.message}`,
          error.type,
          error.statusCode,
          error.details
        );
      }
      
      throw new ServiceError(
        'An unexpected error occurred while deleting journal entry',
        ServiceErrorType.UNKNOWN
      );
    }
  }

  /**
   * Get reflection prompts for journaling
   * @param params Filter parameters
   * @returns List of reflection prompts
   */
  async getReflectionPrompts(params?: {
    category?: string;
    layer?: Types.SoulLayer;
  }): Promise<{
    prompts: Types.ReflectionPrompt[];
  }> {
    try {
      // Construct query string for filtering
      const queryParams = params ? new URLSearchParams() : null;
      
      if (params) {
        if (params.category) queryParams?.append('category', params.category);
        if (params.layer) queryParams?.append('layer', params.layer);
      }
      
      const queryString = queryParams ? `?${queryParams.toString()}` : '';
      
      return await this.fetchData<{ prompts: Types.ReflectionPrompt[] }>(
        `/journal/prompts${queryString}`,
        {
          prompts: [
            {
              id: 'prompt-1',
              text: 'How did today\'s dhikr practice affect your heart?',
              category: 'spiritual_practice',
              layer: 'qalb',
              tags: ['dhikr', 'reflection', 'heart'],
              focus_area: ['connection'],
              display_order: 1,
              status: 'active',
            },
            {
              id: 'prompt-2',
              text: 'Reflect on a moment when you felt Allah\'s mercy today.',
              category: 'gratitude',
              layer: 'ruh',
              tags: ['mercy', 'gratitude', 'awareness'],
              focus_area: ['connection', 'gratitude'],
              display_order: 2,
              status: 'active',
            },
            {
              id: 'prompt-3',
              text: 'What thoughts created distance between you and Allah today?',
              category: 'improvement',
              layer: 'aql',
              tags: ['thoughts', 'mindfulness', 'awareness'],
              focus_area: ['improvement'],
              display_order: 3,
              status: 'active',
            },
            {
              id: 'prompt-4',
              text: 'How did your body respond to stress today, and what Islamic practices helped you manage it?',
              category: 'physical_awareness',
              layer: 'jism',
              tags: ['body', 'stress', 'physical'],
              focus_area: ['self-care'],
              display_order: 4,
              status: 'active',
            },
            {
              id: 'prompt-5',
              text: 'What emotions challenged you today, and how did you respond to them?',
              category: 'emotional_regulation',
              layer: 'nafs',
              tags: ['emotions', 'regulation', 'awareness'],
              focus_area: ['self-control'],
              display_order: 5,
              status: 'active',
            },
          ],
        },
        {
          method: 'GET',
          useCache: true,
        }
      );
    } catch (error) {
      if (error instanceof ServiceError) {
        throw new ServiceError(
          `Failed to retrieve reflection prompts: ${error.message}`,
          error.type,
          error.statusCode,
          error.details
        );
      }
      
      throw new ServiceError(
        'An unexpected error occurred while retrieving reflection prompts',
        ServiceErrorType.UNKNOWN
      );
    }
  }

  /**
   * Save a prompted reflection as a journal entry
   * @param reflection Reflection data
   * @returns The created journal entry
   */
  async saveReflection(reflection: {
    promptId: string;
    content: string;
    mood?: string;
    layers?: Types.SoulLayer[];
  }): Promise<{
    entry: Types.JournalEntry;
    message: string;
  }> {
    try {
      return await this.fetchData<{
        entry: Types.JournalEntry;
        message: string;
      }>(
        '/journal/reflections',
        {
          entry: {
            id: `journal-${Date.now()}`,
            title: 'Daily Reflection',
            content: reflection.content,
            entryType: 'reflection',
            mood: reflection.mood,
            tags: ['prompted', 'reflection'],
            layers: reflection.layers || [],
            entryDate: new Date().toISOString(),
          },
          message: 'Reflection saved successfully',
        },
        {
          method: 'POST',
          body: reflection,
          useCache: false,
        }
      );
    } catch (error) {
      if (error instanceof ServiceError) {
        throw new ServiceError(
          `Failed to save reflection: ${error.message}`,
          error.type,
          error.statusCode,
          error.details
        );
      }
      
      throw new ServiceError(
        'An unexpected error occurred while saving reflection',
        ServiceErrorType.UNKNOWN
      );
    }
  }

  /**
   * Get analytics for journal entries
   * @param timeframe Time period for analytics
   * @returns Journal analytics
   */
  async getAnalytics(timeframe: '7days' | '30days' | '90days' | 'all' = '30days'): Promise<Types.JournalAnalytics> {
    try {
      return await this.fetchData<Types.JournalAnalytics>(
        `/journal/analytics?timeframe=${timeframe}`,
        {
          entryCount: dummyJournalEntries.length,
          moodFrequency: {
            peaceful: 2,
            reflective: 1,
            grateful: 1,
            restless: 1,
          },
          layerFrequency: {
            qalb: 2,
            ruh: 1,
            nafs: 1,
            aql: 1,
            jism: 0,
          },
          tagFrequency: {
            prayer: 2,
            morning: 1,
            connection: 2,
            dhikr: 1,
            work: 1,
            focus: 1,
          },
          entryTypeFrequency: {
            reflection: 3,
            gratitude: 1,
            milestone: 0,
            emergency: 0,
          },
          timeDistribution: {
            morning: 2,
            afternoon: 1,
            evening: 1,
            night: 0,
          },
          streaks: {
            current: 2,
            longest: 3,
          },
        },
        {
          method: 'GET',
          useCache: true,
        }
      );
    } catch (error) {
      if (error instanceof ServiceError) {
        throw new ServiceError(
          `Failed to retrieve journal analytics: ${error.message}`,
          error.type,
          error.statusCode,
          error.details
        );
      }
      
      throw new ServiceError(
        'An unexpected error occurred while retrieving journal analytics',
        ServiceErrorType.UNKNOWN
      );
    }
  }

  /**
   * Generate insights from journal entries
   * @returns Generated insights
   */
  async generateInsights(): Promise<{
    insights: {
      category: string;
      title: string;
      description: string;
      relatedEntries: string[];
      suggestedActions?: string[];
    }[];
  }> {
    try {
      return await this.fetchData<{
        insights: {
          category: string;
          title: string;
          description: string;
          relatedEntries: string[];
          suggestedActions?: string[];
        }[];
      }>(
        '/journal/insights',
        {
          insights: [
            {
              category: 'emotional_pattern',
              title: 'Morning Peace',
              description: 'You consistently feel more peaceful and connected during morning reflections, especially after Fajr prayer.',
              relatedEntries: ['journal-1'],
              suggestedActions: [
                'Continue prioritizing morning dhikr and reflection',
                'Consider extending your morning spiritual routine',
              ],
            },
            {
              category: 'challenge',
              title: 'Work Focus Struggles',
              description: 'You frequently mention difficulty maintaining focus during work hours, often feeling disconnected.',
              relatedEntries: ['journal-2'],
              suggestedActions: [
                'Try short dhikr breaks during work hours',
                'Implement a brief mindfulness practice before starting tasks',
              ],
            },
          ],
        },
        {
          method: 'GET',
          useCache: true,
        }
      );
    } catch (error) {
      if (error instanceof ServiceError) {
        throw new ServiceError(
          `Failed to generate journal insights: ${error.message}`,
          error.type,
          error.statusCode,
          error.details
        );
      }
      
      throw new ServiceError(
        'An unexpected error occurred while generating journal insights',
        ServiceErrorType.UNKNOWN
      );
    }
  }

  /**
   * Record a journey milestone in the journal
   * @param milestone Milestone data
   * @returns The created journal entry
   */
  async recordJourneyMilestone(milestone: {
    journeyId: string;
    milestoneId: string;
    title: string;
    reflection?: string;
    mood?: string;
    tags?: string[];
    layers?: Types.SoulLayer[];
  }): Promise<Types.JournalEntry> {
    try {
      return await this.fetchData<Types.JournalEntry>(
        '/journal/milestones',
        {
          id: `journal-milestone-${Date.now()}`,
          title: `Journey Milestone: ${milestone.title}`,
          content: milestone.reflection || 'Milestone completed',
          entryType: 'milestone',
          mood: milestone.mood,
          tags: milestone.tags || ['journey', 'milestone'],
          layers: milestone.layers || [],
          entryDate: new Date().toISOString(),
        },
        {
          method: 'POST',
          body: milestone,
          useCache: false,
        }
      );
    } catch (error) {
      if (error instanceof ServiceError) {
        throw new ServiceError(
          `Failed to record journey milestone: ${error.message}`,
          error.type,
          error.statusCode,
          error.details
        );
      }
      
      throw new ServiceError(
        'An unexpected error occurred while recording journey milestone',
        ServiceErrorType.UNKNOWN
      );
    }
  }

  /**
   * Export journal entries to a shareable format
   * @param params Export parameters
   * @returns Export data
   */
  async exportJournal(params: {
    format: 'pdf' | 'json' | 'text';
    startDate?: string;
    endDate?: string;
    includeAnalytics?: boolean;
  }): Promise<{
    exportUrl: string;
    expiresAt: string;
  }> {
    try {
      return await this.fetchData<{
        exportUrl: string;
        expiresAt: string;
      }>(
        '/journal/export',
        {
          exportUrl: 'https://cdn.qalbhealing.com/exports/journal-export-123456.pdf',
          expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // 24 hours from now
        },
        {
          method: 'POST',
          body: params,
          useCache: false,
        }
      );
    } catch (error) {
      if (error instanceof ServiceError) {
        throw new ServiceError(
          `Failed to export journal: ${error.message}`,
          error.type,
          error.statusCode,
          error.details
        );
      }
      
      throw new ServiceError(
        'An unexpected error occurred while exporting journal',
        ServiceErrorType.UNKNOWN
      );
    }
  }
}

// Export a singleton instance
const journalService = new JournalService();
export default journalService;

