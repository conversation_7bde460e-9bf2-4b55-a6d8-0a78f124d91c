import React from 'react';
import { StyleSheet, TouchableOpacity, View } from 'react-native';
import { Feather } from '@expo/vector-icons';

import { NameOfAllah } from '../types';
import { Text } from './ui/Text';
import { colors } from '../constants/Colors';
import Theme from '../constants/Theme';

import { useAudioPlayer } from '../hooks/useAudioPlayer';

interface NameOfAllahCardProps {
  name: NameOfAllah;
  isSaved: boolean;
  onSave: () => void;
}

export const NameOfAllahCard: React.FC<NameOfAllahCardProps> = ({
  name,
  isSaved,
  onSave,
}) => {
  const audioPlayer = useAudioPlayer(name.audioUrl);

  return (
    <View style={styles.container}>
      <View style={styles.nameContainer}>
        <Text style={styles.arabicName}>{name.name}</Text>
        <TouchableOpacity
          style={[
            styles.saveButton,
            isSaved && { backgroundColor: colors.primary },
          ]}
          onPress={onSave}
        >
          <Feather
            name={isSaved ? 'bookmark' : ('bookmark-outline' as any)}
            size={18}
          />
        </TouchableOpacity>
      </View>

      <Text variant="subtitle" style={styles.transliteration}>
        {name.transliteration}
      </Text>

      <Text variant="body" color="textSecondary" style={styles.translation}>
        {name.translation}
      </Text>

      <View style={styles.divider} />

      <Text variant="caption" color="textSecondary" style={styles.meaning}>
        {name.meaning}
      </Text>

      {name.benefits && (
        <Text variant="caption" color="primary" style={styles.benefits}>
          <Text variant="caption" style={styles.benefitsLabel} color="primary">
            Benefits:{' '}
          </Text>
          {name.benefits}
        </Text>
      )}

      {name.audioUrl && (
        <>
          <TouchableOpacity
            style={[
              styles.audioButton,
              {
                backgroundColor: audioPlayer.error
                  ? colors.error
                  : colors.accent,
              },
            ]}
            onPress={
              audioPlayer.error
                ? audioPlayer.loadAudio
                : audioPlayer.togglePlayPause
            }
            disabled={audioPlayer.isLoading}
          >
            <Feather
              name={
                audioPlayer.error
                  ? 'refresh-cw'
                  : audioPlayer.isPlaying
                  ? 'pause'
                  : 'play'
              }
              size={16}
              color="#fff"
            />
            <Text variant="caption" color="surface" style={styles.audioText}>
              {audioPlayer.isLoading
                ? 'Loading...'
                : audioPlayer.error
                ? 'Retry'
                : audioPlayer.isPlaying
                ? 'Pause Pronunciation'
                : 'Listen to Pronunciation'}
            </Text>
          </TouchableOpacity>

          {audioPlayer.error && (
            <Text variant="caption" color="error" style={styles.errorText}>
              Audio failed to load. Please try again.
            </Text>
          )}
        </>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: Theme.spacing.m,
    backgroundColor: '#fff',
    borderRadius: Theme.borderRadius.medium,
    ...Theme.shadows.small,
  },
  nameContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Theme.spacing.s,
  },
  arabicName: {
    fontSize: 32,
    fontFamily: Theme.typography.fontFamily.arabic,
    textAlign: 'right',
  },
  saveButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: colors.primary,
  },
  transliteration: {
    marginBottom: Theme.spacing.xs,
  },
  translation: {
    marginBottom: Theme.spacing.m,
  },
  divider: {
    height: 1,
    backgroundColor: '#f0f0f0',
    marginVertical: Theme.spacing.m,
  },
  meaning: {
    marginBottom: Theme.spacing.m,
  },
  benefits: {
    marginBottom: Theme.spacing.m,
  },
  benefitsLabel: {
    fontWeight: '600',
  },
  audioButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: Theme.spacing.s,
    borderRadius: Theme.borderRadius.pill,
  },
  audioText: {
    marginLeft: Theme.spacing.xs,
  },
  errorText: {
    marginTop: Theme.spacing.xs,
    textAlign: 'center',
  },
});
