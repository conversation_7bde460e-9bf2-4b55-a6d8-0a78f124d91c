import { Feather } from '@expo/vector-icons';
import { Stack, useRouter } from 'expo-router';
import React, { useState } from 'react';
import { Image, StyleSheet, TouchableOpacity } from 'react-native';
import { ScrollView } from 'react-native-gesture-handler';

import { Button } from '../components/ui/Button';
import { Card } from '../components/ui/Card';
import { Text } from '../components/ui/Text';
import { View } from '../components/ui/View';
import { colors } from '../constants/Colors';
import Theme from '../constants/Theme';

export default function JourneysScreen() {
  const router = useRouter();

  const [activeTab, setActiveTab] = useState('featured');

  const tabs = [
    { id: 'featured', label: 'Featured' },
    { id: 'body', label: 'Jism' },
    { id: 'emotions', label: 'Nafs' },
    { id: 'mind', label: 'Aql' },
    { id: 'heart', label: 'Qalb' },
    { id: 'soul', label: 'Ruh' },
  ];

  const journeys = [
    {
      id: '1',
      title: '7-Day Anxiety Relief',
      description:
        'A short journey focusing on immediate relief through dhikr, dua, and mindfulness.',
      days: 7,
      category: ['emotions', 'mind'],
      color: colors.secondary,
      imageUrl:
        'https://images.pexels.com/photos/1261728/pexels-photo-1261728.jpeg',
    },
    {
      id: '2',
      title: '21-Day Heart Purification',
      description:
        'Cleanse and purify the heart through daily reflections, Quranic verses, and spiritual practices.',
      days: 21,
      category: ['heart', 'soul'],
      color: colors.primary,
      imageUrl:
        'https://images.pexels.com/photos/1261728/pexels-photo-1261728.jpeg',
    },
    {
      id: '3',
      title: '40-Day Transformation',
      description:
        'A comprehensive journey addressing all five layers for complete spiritual transformation.',
      days: 40,
      category: ['body', 'emotions', 'mind', 'heart', 'soul'],
      color: colors.accent,
      imageUrl:
        'https://images.pexels.com/photos/1261728/pexels-photo-1261728.jpeg',
    },
  ];

  const filteredJourneys =
    activeTab === 'featured'
      ? journeys
      : journeys.filter((journey) => journey.category.includes(activeTab));

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'body':
        return colors.error;
      case 'emotions':
        return colors.secondary;
      case 'mind':
        return colors.aqlYellow;
      case 'heart':
        return colors.primary;
      case 'soul':
        return colors.accent;
      default:
        return colors.primary;
    }
  };

  return (
    <>
      <Stack.Screen options={{ title: 'Healing Journeys' }} />
      <ScrollView
        style={styles.container}
        contentContainerStyle={styles.contentContainer}
      >
        <View style={styles.header}>
          <Text variant="heading2">Healing Journeys</Text>
          <Text variant="body" color="textSecondary" style={styles.subtitle}>
            Structured paths for spiritual healing and transformation
          </Text>
        </View>

        <View style={styles.tabsContainer}>
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            style={styles.tabsScroll}
          >
            {tabs.map((tab) => (
              <TouchableOpacity
                key={tab.id}
                style={[
                  styles.tab,
                  activeTab === tab.id && {
                    backgroundColor: getCategoryColor(tab.id),
                    borderColor: getCategoryColor(tab.id),
                  },
                ]}
                onPress={() => setActiveTab(tab.id)}
              >
                <Text variant="caption">{tab.label}</Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>

        <View style={styles.journeysContainer}>
          {filteredJourneys.map((journey) => (
            <Card
              key={journey.id}
              variant="elevated"
              style={styles.journeyCard}
            >
              <TouchableOpacity
                onPress={() => console.log(`View journey: ${journey.id}`)}
              >
                <View style={styles.journeyHeader}>
                  <Image
                    source={{ uri: journey.imageUrl }}
                    style={styles.journeyImage}
                  />
                  <View
                    style={[
                      styles.journeyDays,
                      { backgroundColor: journey.color },
                    ]}
                  >
                    <Text variant="heading3" color="surface">
                      {journey.days}
                    </Text>
                    <Text variant="caption" color="surface">
                      days
                    </Text>
                  </View>
                </View>
                <View style={styles.journeyContent}>
                  <Text variant="subtitle" style={styles.journeyTitle}>
                    {journey.title}
                  </Text>
                  <Text
                    variant="body"
                    color="textSecondary"
                    style={styles.journeyDescription}
                  >
                    {journey.description}
                  </Text>
                  <View style={styles.categoriesRow}>
                    {journey.category.map((cat) => (
                      <View
                        key={cat}
                        style={[
                          styles.categoryBadge,
                          { backgroundColor: getCategoryColor(cat) },
                        ]}
                      >
                        <Text variant="caption" color="surface">
                          {cat}
                        </Text>
                      </View>
                    ))}
                  </View>
                  <Button
                    title="Start Journey"
                    variant="primary"
                    size="medium"
                    style={[
                      styles.startButton,
                      { backgroundColor: journey.color },
                    ]}
                    onPress={() => console.log(`Start journey: ${journey.id}`)}
                  />
                </View>
              </TouchableOpacity>
            </Card>
          ))}
        </View>

        <Card variant="elevated" style={styles.customCard}>
          <View style={styles.customContent}>
            <Feather name="star" size={32} style={styles.customIcon} />
            <Text variant="subtitle" style={styles.customTitle}>
              Create Custom Journey
            </Text>
            <Text
              variant="body"
              color="textSecondary"
              style={styles.customDescription}
            >
              Design your own healing journey tailored to your specific needs
              and goals.
            </Text>
            <Button
              title="Create Journey"
              variant="primary"
              size="medium"
              style={styles.customButton}
              onPress={() => console.log('Create custom journey')}
            />
          </View>
        </Card>

        <View style={styles.footer}>
          <Button
            title="Return to Home"
            variant="outline"
            size="medium"
            onPress={() => router.back()}
          />
        </View>
      </ScrollView>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  contentContainer: {
    padding: Theme.spacing.m,
    paddingBottom: Theme.spacing.xl * 2,
  },
  header: {
    marginBottom: Theme.spacing.l,
  },
  subtitle: {
    marginTop: Theme.spacing.xs,
  },
  tabsContainer: {
    marginBottom: Theme.spacing.m,
  },
  tabsScroll: {
    marginLeft: -Theme.spacing.xs,
  },
  tab: {
    paddingHorizontal: Theme.spacing.m,
    paddingVertical: Theme.spacing.s,
    marginRight: Theme.spacing.s,
    borderRadius: Theme.borderRadius.pill,
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  journeysContainer: {
    marginBottom: Theme.spacing.l,
  },
  journeyCard: {
    marginBottom: Theme.spacing.m,
    overflow: 'hidden',
  },
  journeyHeader: {
    width: '100%',
    height: 150,
    position: 'relative',
  },
  journeyImage: {
    width: '100%',
    height: '100%',
  },
  journeyDays: {
    position: 'absolute',
    bottom: -20,
    right: 20,
    width: 60,
    height: 60,
    borderRadius: 30,
    justifyContent: 'center',
    alignItems: 'center',
    ...Theme.shadows.medium,
  },
  journeyContent: {
    padding: Theme.spacing.m,
    paddingTop: Theme.spacing.l,
  },
  journeyTitle: {
    marginBottom: Theme.spacing.s,
  },
  journeyDescription: {
    marginBottom: Theme.spacing.m,
  },
  categoriesRow: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: Theme.spacing.m,
  },
  categoryBadge: {
    paddingHorizontal: Theme.spacing.s,
    paddingVertical: Theme.spacing.xs,
    borderRadius: Theme.borderRadius.pill,
    marginRight: Theme.spacing.xs,
    marginBottom: Theme.spacing.xs,
  },
  startButton: {
    alignSelf: 'center',
    minWidth: 150,
  },
  customCard: {
    marginTop: Theme.spacing.l,
    marginBottom: Theme.spacing.l,
  },
  customContent: {
    padding: Theme.spacing.m,
    alignItems: 'center',
  },
  customIcon: {
    marginBottom: Theme.spacing.m,
  },
  customTitle: {
    marginBottom: Theme.spacing.s,
    textAlign: 'center',
  },
  customDescription: {
    textAlign: 'center',
    marginBottom: Theme.spacing.m,
  },
  customButton: {
    minWidth: 150,
  },
  footer: {
    marginTop: Theme.spacing.l,
    alignItems: 'center',
  },
});
