import React, { useState } from 'react';
import { StyleSheet, TouchableOpacity } from 'react-native';
import { ScrollView } from 'react-native-gesture-handler';
import { Feather } from '@expo/vector-icons';
import { Stack, useRouter } from 'expo-router';

import { Button } from '../components/ui/Button';
import { Card } from '../components/ui/Card';
import { Text } from '../components/ui/Text';
import { View } from '../components/ui/View';
import { colors } from '../constants/Colors';
import Theme from '../constants/Theme';

export default function EmergencyScreen() {
  const router = useRouter();

  const [breathCount, setBreathCount] = useState(0);
  const [isBreathing, setIsBreathing] = useState(false);

  const startBreathingExercise = () => {
    setIsBreathing(true);
    // In a real implementation, this would start a guided breathing animation
    console.log('Starting breathing exercise');
  };

  return (
    <>
      <Stack.Screen options={{ title: 'Sakīna Mode' }} />
      <ScrollView
        style={styles.container}
        contentContainerStyle={styles.contentContainer}
      >
        <View style={styles.header}>
          <Text variant="heading2">Sakīna Mode</Text>
          <Text variant="body" color="textSecondary" style={styles.subtitle}>
            A moment of calm when you need it most
          </Text>
        </View>

        <Card
          variant="elevated"
          style={[styles.card, { backgroundColor: colors.error }]}
        >
          <View style={styles.cardContent}>
            <Feather
              name="heart"
              size={32}
              color="#fff"
              style={styles.cardIcon}
            />
            <Text variant="subtitle" color="surface" style={styles.cardTitle}>
              Guided Breathing
            </Text>
            <Text variant="body" color="surface" style={styles.cardDescription}>
              Take a moment to breathe deeply and center yourself with Allah's
              remembrance.
            </Text>
            <Button
              title={isBreathing ? 'Continue Breathing' : 'Start Breathing'}
              variant="outline"
              size="medium"
              style={styles.cardButton}
              textStyle={{ color: '#fff' }}
              buttonStyle={{ borderColor: '#fff' }}
              onPress={startBreathingExercise}
            />
          </View>
        </Card>

        <Card variant="elevated" style={styles.card}>
          <View style={styles.cardContent}>
            <Text variant="subtitle" style={styles.cardTitle}>
              Ruqyah Verses
            </Text>
            <Text
              variant="body"
              color="textSecondary"
              style={styles.cardDescription}
            >
              Listen to healing verses from the Quran to bring peace to your
              heart and mind.
            </Text>
            <Button
              title="Listen Now"
              variant="primary"
              size="medium"
              style={styles.cardButton}
              onPress={() => console.log('Listen to Ruqyah')}
            />
          </View>
        </Card>

        <Card variant="elevated" style={styles.card}>
          <View style={styles.cardContent}>
            <Text variant="subtitle" style={styles.cardTitle}>
              Healing Du'as
            </Text>
            <Text
              variant="body"
              color="textSecondary"
              style={styles.cardDescription}
            >
              Supplications from the Sunnah for relief, comfort, and strength in
              difficult times.
            </Text>
            <Button
              title="View Du'as"
              variant="primary"
              size="medium"
              style={styles.cardButton}
              onPress={() => console.log('View Duas')}
            />
          </View>
        </Card>

        <View style={styles.footer}>
          <Button
            title="Return to Home"
            variant="outline"
            size="medium"
            onPress={() => router.back()}
          />
        </View>
      </ScrollView>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  contentContainer: {
    padding: Theme.spacing.m,
    paddingBottom: Theme.spacing.xl * 2,
  },
  header: {
    marginBottom: Theme.spacing.l,
  },
  subtitle: {
    marginTop: Theme.spacing.xs,
  },
  card: {
    marginBottom: Theme.spacing.m,
  },
  cardContent: {
    padding: Theme.spacing.m,
    alignItems: 'center',
  },
  cardIcon: {
    marginBottom: Theme.spacing.m,
  },
  cardTitle: {
    marginBottom: Theme.spacing.s,
    textAlign: 'center',
  },
  cardDescription: {
    textAlign: 'center',
    marginBottom: Theme.spacing.m,
  },
  cardButton: {
    minWidth: 150,
  },
  footer: {
    marginTop: Theme.spacing.l,
    alignItems: 'center',
  },
});
