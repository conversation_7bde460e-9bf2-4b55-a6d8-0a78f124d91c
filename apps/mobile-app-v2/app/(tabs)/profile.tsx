import React, { useState } from 'react';
import { StyleSheet, TouchableOpacity, Image } from 'react-native';
import { ScrollView } from 'react-native-gesture-handler';
import { Feather } from '@expo/vector-icons';

import { Text } from '../../components/ui/Text';
import { View } from '../../components/ui/View';
import { Card } from '../../components/ui/Card';
import { colors } from "../../constants/Colors";
import Theme from '../../constants/Theme';

export default function ProfileScreen() {

  const styles = createStyles(colors);

  // Mock user data
  const [user, setUser] = useState({
    name: 'Isa',
    email: '<EMAIL>',
    joinDate: 'May 2023',
    streakDays: 7,
    journeysCompleted: 2,
    journeysInProgress: 1,
  });

  return (
    <ScrollView
      style={styles.container}
      contentContainerStyle={styles.contentContainer}
    >
      <View style={styles.header}>
        <View style={styles.profileImageContainer}>
          <Image
            source={{ uri: 'https://randomuser.me/api/portraits/women/65.jpg' }}
            style={styles.profileImage}
          />
          <TouchableOpacity style={styles.editImageButton}>
            <Feather name="edit-2" size={16} color="#fff" />
          </TouchableOpacity>
        </View>

        <Text variant="heading2" style={styles.userName}>
          {user.name}
        </Text>
        <Text variant="body" color="textSecondary" style={styles.userEmail}>
          {user.email}
        </Text>

        <Text variant="caption" color="textSecondary" style={styles.joinDate}>
          Member since {user.joinDate}
        </Text>
      </View>

      <View style={styles.statsContainer}>
        <View style={styles.statItem}>
          <Text variant="heading2" color="primary">
            {user.streakDays}
          </Text>
          <Text variant="caption" color="textSecondary">
            Current Streak
          </Text>
        </View>

        <View style={styles.statDivider} />

        <View style={styles.statItem}>
          <Text variant="heading2" color="primary">
            {user.journeysCompleted}
          </Text>
          <Text variant="caption" color="textSecondary">
            Journeys Completed
          </Text>
        </View>

        <View style={styles.statDivider} />

        <View style={styles.statItem}>
          <Text variant="heading2" color="primary">
            {user.journeysInProgress}
          </Text>
          <Text variant="caption" color="textSecondary">
            Journeys In Progress
          </Text>
        </View>
      </View>

      <View style={styles.menuSection}>
        <Card variant="elevated" style={styles.menuCard}>
          <TouchableOpacity style={styles.menuItem}>
            <Text variant="subtitle">Account Settings</Text>
          </TouchableOpacity>

          <View style={styles.menuDivider} />

          <TouchableOpacity style={styles.menuItem}>
            <Text variant="subtitle">Saved Resources</Text>
          </TouchableOpacity>

          <View style={styles.menuDivider} />

          <TouchableOpacity style={styles.menuItem}>
            <Text variant="subtitle">Notifications</Text>
          </TouchableOpacity>

          <View style={styles.menuDivider} />

          <TouchableOpacity style={styles.menuItem}>
            <Text variant="subtitle">Help & Support</Text>
          </TouchableOpacity>

          <View style={styles.menuDivider} />

          <TouchableOpacity style={styles.menuItem}>
            <Text variant="subtitle" style={{ color: colors.error }}>
              Log Out
            </Text>
          </TouchableOpacity>
        </Card>
      </View>
    </ScrollView>
  );
}

const createStyles = (colors: any) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: '#f8f9fa',
    },
    contentContainer: {
      padding: Theme.spacing.m,
      paddingBottom: Theme.spacing.xl * 2,
    },
    header: {
      alignItems: 'center',
      marginBottom: Theme.spacing.l,
    },
    profileImageContainer: {
      position: 'relative',
      marginBottom: Theme.spacing.m,
    },
    profileImage: {
      width: 100,
      height: 100,
      borderRadius: 50,
      borderWidth: 3,
      borderColor: '#fff',
    },
    editImageButton: {
      position: 'absolute',
      right: 0,
      bottom: 0,
      backgroundColor: colors.primary,
      width: 30,
      height: 30,
      borderRadius: 15,
      alignItems: 'center',
      justifyContent: 'center',
      borderWidth: 2,
      borderColor: '#fff',
    },
    userName: {
      marginBottom: Theme.spacing.xs,
    },
    userEmail: {
      marginBottom: Theme.spacing.s,
    },
    joinDate: {
      marginTop: Theme.spacing.xs,
    },
    statsContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      backgroundColor: '#fff',
      borderRadius: Theme.borderRadius.medium,
      padding: Theme.spacing.m,
      marginBottom: Theme.spacing.m,
      ...Theme.shadows.small,
    },
    statItem: {
      alignItems: 'center',
      flex: 1,
    },
    statDivider: {
      width: 1,
      height: '80%',
      backgroundColor: '#f0f0f0',
    },
    menuSection: {
      marginBottom: Theme.spacing.l,
    },
    menuCard: {
      padding: 0,
      overflow: 'hidden',
    },
    menuItem: {
      flexDirection: 'row',
      alignItems: 'center',
      padding: Theme.spacing.m,
    },
    menuIcon: {
      marginRight: Theme.spacing.m,
    },
    menuArrow: {
      marginLeft: 'auto',
    },
    menuDivider: {
      height: 1,
      backgroundColor: '#f0f0f0',
      marginLeft: Theme.spacing.m,
    },
  });
