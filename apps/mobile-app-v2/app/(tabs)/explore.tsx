import React from 'react';
import { StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { Feather } from '@expo/vector-icons';
import { useRouter } from 'expo-router';

import { Button } from '../../src/components/ui/Button';
import { Card } from '../../src/components/ui/Card';
import { Text } from '../../src/components/ui/Text';
import { View } from '../../src/components/ui/View';
import { colors } from '../../src/constants/Colors';
import Theme from '../../src/constants/Theme';

export default function ExploreScreen() {
  const router = useRouter();

  // Categories for healing journeys and resources
  const categories = [
    {
      id: '1',
      title: 'Healing Journeys',
      description: 'Structured paths for spiritual growth and healing',
      icon: 'compass',
      color: colors.primary,
      route: 'journeys',
    },
    {
      id: '2',
      title: 'Knowledge Hub',
      description: 'Articles, videos, and resources for Islamic healing',
      icon: 'book-open',
      color: colors.accent,
      route: 'knowledge',
    },
    {
      id: '3',
      title: 'Daily Practices',
      description: 'Dhikr, dua, and prophetic routines',
      icon: 'repeat',
      color: colors.secondary,
      route: 'practices',
    },
    {
      id: '4',
      title: 'Community',
      description: 'Connect with others on similar healing journeys',
      icon: 'users',
      color: colors.error,
      route: 'community',
    },
  ];

  return (
    <ScrollView
      style={styles.container}
      contentContainerStyle={styles.contentContainer}
    >
      <View style={styles.header}>
        <Text variant="heading2">Explore</Text>
        <Text variant="body" color="textSecondary" style={styles.subtitle}>
          Discover healing journeys, resources, and practices
        </Text>
      </View>

      <View style={styles.categoriesContainer}>
        {categories.map((category) => (
          <Card
            key={category.id}
            variant="elevated"
            style={styles.categoryCard}
          >
            <TouchableOpacity
              style={styles.categoryContent}
              onPress={() => router.push(category.route)}
            >
              <View
                style={[
                  styles.categoryIcon,
                  { backgroundColor: category.color },
                ]}
              >
                <Feather name={category.icon as any} size={24} color="#fff" />
              </View>
              <View style={styles.categoryInfo}>
                <Text variant="subtitle">{category.title}</Text>
                <Text
                  variant="body"
                  color="textSecondary"
                  style={styles.categoryDescription}
                >
                  {category.description}
                </Text>
              </View>
              <Feather name="chevron-right" size={20} style={styles.chevron} />
            </TouchableOpacity>
          </Card>
        ))}
      </View>

      <View style={styles.featuredSection}>
        <Text variant="subtitle" style={styles.sectionTitle}>
          Featured Content
        </Text>

        <Card variant="elevated" style={styles.featuredCard}>
          <View style={styles.featuredContent}>
            <Text variant="subtitle">Names of Allah Exploration</Text>
            <Text
              variant="body"
              color="textSecondary"
              style={styles.featuredDescription}
            >
              Discover the healing powers of Allah's 99 names and how they can
              guide your spiritual journey.
            </Text>
            <Button
              title="Explore Names"
              variant="primary"
              size="medium"
              onPress={() => router.push('knowledge')}
              style={styles.featuredButton}
            />
          </View>
        </Card>

        <Card variant="elevated" style={styles.featuredCard}>
          <View style={styles.featuredContent}>
            <Text variant="subtitle">Healing through Quranic Verses</Text>
            <Text
              variant="body"
              color="textSecondary"
              style={styles.featuredDescription}
            >
              Learn how specific verses from the Quran can help heal different
              aspects of your being.
            </Text>
            <Button
              title="Explore Verses"
              variant="primary"
              size="medium"
              onPress={() => router.push('knowledge')}
              style={styles.featuredButton}
            />
          </View>
        </Card>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  contentContainer: {
    padding: Theme.spacing.m,
    paddingBottom: Theme.spacing.xl * 2,
  },
  header: {
    marginBottom: Theme.spacing.l,
  },
  subtitle: {
    marginTop: Theme.spacing.xs,
  },
  categoriesContainer: {
    marginBottom: Theme.spacing.l,
  },
  categoryCard: {
    marginBottom: Theme.spacing.m,
    overflow: 'hidden',
  },
  categoryContent: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: Theme.spacing.m,
  },
  categoryIcon: {
    width: 50,
    height: 50,
    borderRadius: 25,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: Theme.spacing.m,
  },
  categoryInfo: {
    flex: 1,
  },
  categoryDescription: {
    marginTop: Theme.spacing.xs,
  },
  chevron: {
    marginLeft: Theme.spacing.s,
  },
  featuredSection: {
    marginBottom: Theme.spacing.l,
  },
  sectionTitle: {
    marginBottom: Theme.spacing.m,
  },
  featuredCard: {
    marginBottom: Theme.spacing.m,
  },
  featuredContent: {
    padding: Theme.spacing.m,
  },
  featuredDescription: {
    marginVertical: Theme.spacing.m,
  },
  featuredButton: {
    alignSelf: 'flex-start',
  },
});
