/**
 * Assessment Flow Screen for Feature 1: Understanding Your Inner Landscape (REVISED)
 * Main assessment experience with experience-first symptom selection
 */

import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
  Alert,
  ActivityIndicator,
  Animated,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { router, useLocalSearchParams } from 'expo-router';

import { assessmentService } from '../../services/assessment.service';
import { colors } from '../../constants/Colors';

interface AssessmentQuestion {
  id: string;
  category: 'physical' | 'emotional' | 'mental' | 'spiritual';
  layer: 'jism' | 'nafs' | 'aql' | 'qalb' | 'ruh';
  title: string;
  description?: string;
  symptoms: AssessmentSymptom[];
  reflectionPrompt?: string;
  reflectionRequired: boolean;
  allowMultipleSelection: boolean;
  intensityScale: boolean;
  customInputAllowed: boolean;
}

interface AssessmentSymptom {
  id: string;
  text: string;
  description?: string;
  severity: 'mild' | 'moderate' | 'severe';
  primaryLayer: 'jism' | 'nafs' | 'aql' | 'qalb' | 'ruh';
}

interface SymptomCategory {
  id: string;
  name: string;
  layer: 'jism' | 'nafs' | 'aql' | 'qalb' | 'ruh';
  symptoms: string[];
  intensity: 'mild' | 'moderate' | 'severe';
  userReflection?: string;
}

export default function AssessmentFlowScreen() {
  const { sessionId } = useLocalSearchParams();

  const [currentStep, setCurrentStep] = useState('physical_experiences');
  const [questions, setQuestions] = useState<AssessmentQuestion[]>([]);
  const [responses, setResponses] = useState<SymptomCategory>({
    id: '',
    name: '',
    layer: 'jism',
    symptoms: [],
    intensity: 'mild',
  });
  const [reflection, setReflection] = useState('');
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [progress, setProgress] = useState(0);
  const [crisisModal, setCrisisModal] = useState<any>(null);

  const stepStartTime = useRef(Date.now());
  const fadeAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    loadCurrentStep();
    startTimer();

    // Fade in animation
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 500,
      useNativeDriver: true,
    }).start();

    return () => {
      // Save time spent when component unmounts
      const timeSpentOnStep = Math.floor(
        (Date.now() - stepStartTime.current) / 1000
      );
    };
  }, [currentStep]);

  const startTimer = () => {
    stepStartTime.current = Date.now();
  };

  const loadCurrentStep = async () => {
    try {
      setLoading(true);
      const stepQuestions = await assessmentService.getAssessmentQuestions(
        sessionId as string,
        currentStep
      );
      setQuestions(stepQuestions);

      // Initialize responses for this step
      if (stepQuestions.length > 0) {
        const question = stepQuestions[0];
        setResponses({
          id: question.id,
          name: question.title,
          layer: question.layer,
          symptoms: [],
          intensity: 'mild',
        });
      }
    } catch (err) {
      console.error('Error loading step:', err);
      Alert.alert('Error', 'Failed to load assessment step. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleSymptomSelection = (symptoms: string[]) => {
    setResponses((prev) => ({
      ...prev,
      symptoms,
    }));
  };

  const handleIntensityChange = (intensity: 'mild' | 'moderate' | 'severe') => {
    setResponses((prev) => ({
      ...prev,
      intensity,
    }));
  };

  const handleReflectionChange = (text: string) => {
    setReflection(text);
  };

  const handleNext = async () => {
    try {
      setSubmitting(true);

      // Calculate time spent on this step
      const stepTime = Math.floor((Date.now() - stepStartTime.current) / 1000);

      // Prepare submission data
      const submissionData = {
        ...responses,
        userReflection: reflection,
      };

      // Submit response
      const result = await assessmentService.submitAssessmentResponse(
        sessionId as string,
        currentStep,
        submissionData,
        stepTime
      );

      // Handle crisis detection
      if (result.crisisDetected) {
        setCrisisModal({
          level: result.crisisLevel,
          message: result.message,
          actions: result.emergencyActions,
        });
        return;
      }

      // Update progress
      setProgress(result.progress);

      // Navigate to next step or results
      if (result.nextStep) {
        setCurrentStep(result.nextStep);
        setResponses({
          id: '',
          name: '',
          layer: 'jism',
          symptoms: [],
          intensity: 'mild',
        });
        setReflection('');

        // Fade out and in for step transition
        Animated.sequence([
          Animated.timing(fadeAnim, {
            toValue: 0,
            duration: 200,
            useNativeDriver: true,
          }),
          Animated.timing(fadeAnim, {
            toValue: 1,
            duration: 300,
            useNativeDriver: true,
          }),
        ]).start();
      } else {
        // Assessment complete, navigate to diagnosis
        router.push({
          pathname: '/assessment/results',
          params: { sessionId },
        });
      }
    } catch (err) {
      console.error('Error submitting response:', err);
      Alert.alert('Error', 'Failed to submit response. Please try again.');
    } finally {
      setSubmitting(false);
    }
  };

  const handleBack = () => {
    router.back();
  };

  const handleCrisisAction = (actionId: string) => {
    setCrisisModal(null);

    switch (actionId) {
      case 'emergency_sakina':
        router.push('/emergency');
        break;
      case 'crisis_counselor':
        router.push('/crisis-counselor');
        break;
      case 'crisis_hotline':
        router.push('/crisis-hotline');
        break;
      default:
        // Continue with assessment
        break;
    }
  };

  const getStepTitle = () => {
    const stepTitles = {
      physical_experiences: 'Physical Experiences',
      emotional_experiences: 'Emotional Experiences',
      mental_experiences: 'Mental Experiences',
      spiritual_experiences: 'Spiritual & Soul Experiences',
      reflections: 'Personal Reflections',
    };
    return stepTitles[currentStep] || 'Assessment';
  };

  const getStepDescription = () => {
    const descriptions = {
      physical_experiences: "Let's start with what your body is telling you:",
      emotional_experiences: "Now, let's explore your emotional landscape:",
      mental_experiences: "Let's look at what's happening in your mind:",
      spiritual_experiences:
        "Finally, let's explore your spiritual and soul experiences:",
      reflections: 'Take a moment to reflect on your experiences:',
    };
    return descriptions[currentStep] || '';
  };

  const canProceed = () => {
    if (currentStep === 'reflections') {
      return reflection.trim().length > 0;
    }
    return responses.symptoms.length > 0;
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={colors.primary} />
        <Text style={styles.loadingText}>Loading assessment...</Text>
      </View>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <LinearGradient
        colors={['#E8F5E8', '#F0F8F0', '#FFFFFF']}
        style={styles.gradient}
      >
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity style={styles.backButton} onPress={handleBack}>
            <Ionicons name="arrow-back" size={24} color={colors.text} />
          </TouchableOpacity>
          <View style={styles.headerContent}>
            <Text style={styles.headerTitle}>{getStepTitle()}</Text>
            <View style={styles.progressBar}>
              <View style={[styles.progressFill, { width: `${progress}%` }]} />
            </View>
          </View>
        </View>

        <Animated.View style={[styles.content, { opacity: fadeAnim }]}>
          <ScrollView
            style={styles.scrollView}
            showsVerticalScrollIndicator={false}
          >
            {/* Step Description */}
            <View style={styles.descriptionContainer}>
              <Text style={styles.stepDescription}>{getStepDescription()}</Text>
            </View>

            {/* Assessment Content */}
            {currentStep === 'reflections' ? (
              <View style={styles.reflectionContainer}>
                <Text style={styles.reflectionPrompt}>
                  Share your thoughts and feelings about your experiences...
                </Text>
                {/* Add reflection input component here */}
              </View>
            ) : (
              questions.length > 0 && (
                <View style={styles.symptomsContainer}>
                  {/* Add symptom selector component here */}
                  <Text style={styles.symptomsTitle}>
                    Select your experiences:
                  </Text>
                  {questions[0].symptoms.map((symptom) => (
                    <TouchableOpacity
                      key={symptom.id}
                      style={[
                        styles.symptomItem,
                        responses.symptoms.includes(symptom.id) &&
                          styles.symptomItemSelected,
                      ]}
                      onPress={() => {
                        const newSymptoms = responses.symptoms.includes(
                          symptom.id
                        )
                          ? responses.symptoms.filter((s) => s !== symptom.id)
                          : [...responses.symptoms, symptom.id];
                        handleSymptomSelection(newSymptoms);
                      }}
                    >
                      <Text
                        style={[
                          styles.symptomText,
                          responses.symptoms.includes(symptom.id) &&
                            styles.symptomTextSelected,
                        ]}
                      >
                        {symptom.text}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </View>
              )
            )}

            {/* Islamic Comfort */}
            <View style={styles.comfortContainer}>
              <View style={styles.comfortIcon}>
                <Ionicons name="heart" size={20} color={colors.primary} />
              </View>
              <Text style={styles.comfortText}>
                "And whoever relies upon Allah - then He is sufficient for him.
                Indeed, Allah will accomplish His purpose." - Quran 65:3
              </Text>
            </View>
          </ScrollView>

          {/* Action Buttons */}
          <View style={styles.actionsContainer}>
            <TouchableOpacity
              style={[
                styles.nextButton,
                !canProceed() && styles.nextButtonDisabled,
              ]}
              onPress={handleNext}
              disabled={!canProceed() || submitting}
            >
              <LinearGradient
                colors={
                  canProceed()
                    ? [colors.primary, colors.primaryLight]
                    : ['#ccc', '#ccc']
                }
                style={styles.nextButtonGradient}
              >
                {submitting ? (
                  <ActivityIndicator size="small" color="white" />
                ) : (
                  <>
                    <Text style={styles.nextButtonText}>
                      {currentStep === 'reflections'
                        ? 'Complete Assessment'
                        : 'Continue'}
                    </Text>
                    <Ionicons name="arrow-forward" size={20} color="white" />
                  </>
                )}
              </LinearGradient>
            </TouchableOpacity>

            {/* Skip Option for Non-Required Steps */}
            {currentStep !== 'reflections' && (
              <TouchableOpacity style={styles.skipButton} onPress={handleNext}>
                <Text style={styles.skipButtonText}>Skip this section</Text>
              </TouchableOpacity>
            )}
          </View>
        </Animated.View>

        {/* Crisis Modal */}
        {crisisModal && (
          <View style={styles.crisisModal}>
            <View style={styles.crisisModalContent}>
              <Text style={styles.crisisModalTitle}>
                Immediate Support Available
              </Text>
              <Text style={styles.crisisModalMessage}>
                {crisisModal.message}
              </Text>
              <View style={styles.crisisModalActions}>
                {crisisModal.actions?.map((action, index) => (
                  <TouchableOpacity
                    key={index}
                    style={[
                      styles.crisisAction,
                      action.primary && styles.crisisActionPrimary,
                    ]}
                    onPress={() => handleCrisisAction(action.id)}
                  >
                    <Text
                      style={[
                        styles.crisisActionText,
                        action.primary && styles.crisisActionTextPrimary,
                      ]}
                    >
                      {action.text}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>
          </View>
        )}
      </LinearGradient>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  gradient: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: colors.textSecondary,
    marginTop: 16,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 24,
    paddingTop: 16,
  },
  backButton: {
    marginRight: 16,
  },
  headerContent: {
    flex: 1,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: colors.text,
    marginBottom: 8,
  },
  progressBar: {
    height: 4,
    backgroundColor: '#E0E0E0',
    borderRadius: 2,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    backgroundColor: colors.primary,
    borderRadius: 2,
  },
  content: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
    padding: 24,
  },
  descriptionContainer: {
    marginBottom: 32,
  },
  stepDescription: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text,
    lineHeight: 28,
  },
  reflectionContainer: {
    marginBottom: 24,
  },
  reflectionPrompt: {
    fontSize: 16,
    color: colors.textSecondary,
    marginBottom: 16,
  },
  symptomsContainer: {
    marginBottom: 24,
  },
  symptomsTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text,
    marginBottom: 16,
  },
  symptomItem: {
    backgroundColor: 'white',
    padding: 16,
    borderRadius: 12,
    marginBottom: 8,
    borderWidth: 1,
    borderColor: '#E0E0E0',
  },
  symptomItemSelected: {
    borderColor: colors.primary,
    backgroundColor: colors.primary + '10',
  },
  symptomText: {
    fontSize: 16,
    color: colors.text,
  },
  symptomTextSelected: {
    color: colors.primary,
    fontWeight: '500',
  },
  comfortContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    backgroundColor: colors.surface,
    padding: 24,
    borderRadius: 12,
    marginTop: 32,
  },
  comfortIcon: {
    marginRight: 16,
    marginTop: 2,
  },
  comfortText: {
    fontSize: 16,
    color: colors.textSecondary,
    fontStyle: 'italic',
    flex: 1,
    lineHeight: 22,
  },
  actionsContainer: {
    padding: 24,
    gap: 16,
  },
  nextButton: {
    borderRadius: 12,
    overflow: 'hidden',
  },
  nextButtonDisabled: {
    opacity: 0.6,
  },
  nextButtonGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 24,
    gap: 8,
  },
  nextButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: 'white',
  },
  skipButton: {
    alignItems: 'center',
    padding: 16,
  },
  skipButtonText: {
    fontSize: 16,
    color: colors.textSecondary,
    textDecorationLine: 'underline',
  },
  crisisModal: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  crisisModalContent: {
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 24,
    width: '100%',
    maxWidth: 400,
  },
  crisisModalTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: colors.error,
    marginBottom: 16,
    textAlign: 'center',
  },
  crisisModalMessage: {
    fontSize: 16,
    color: colors.text,
    lineHeight: 24,
    marginBottom: 24,
    textAlign: 'center',
  },
  crisisModalActions: {
    gap: 12,
  },
  crisisAction: {
    backgroundColor: colors.surface,
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
  },
  crisisActionPrimary: {
    backgroundColor: colors.primary,
  },
  crisisActionText: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text,
  },
  crisisActionTextPrimary: {
    color: 'white',
  },
});
