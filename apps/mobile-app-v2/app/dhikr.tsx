import React, { useState, useEffect, useRef } from 'react';
import {
  StyleSheet,
  TouchableOpacity,
  Animated,
  Vibration,
  View as RNView,
} from 'react-native';
import { ScrollView } from 'react-native-gesture-handler';
import { Feather } from '@expo/vector-icons';
import { Stack, useRouter } from 'expo-router';
import * as Haptics from 'expo-haptics';

import { Button } from '../components/ui/Button';
import { Card } from '../components/ui/Card';
import { Text } from '../components/ui/Text';
import { View } from '../components/ui/View';
import { colors } from '../constants/Colors';
import Theme from '../constants/Theme';

interface DhikrPreset {
  id: string;
  title: string;
  arabicText: string;
  transliteration: string;
  translation: string;
  recommendedCount: number;
  color: string;
}

export default function DhikrScreen() {
  const router = useRouter();
  const [count, setCount] = useState(0);
  const [targetCount, setTargetCount] = useState(33);
  const [activePreset, setActivePreset] = useState<string | null>(null);
  const [completedSessions, setCompletedSessions] = useState(0);
  const [isResetting, setIsResetting] = useState(false);
  const scaleAnim = useRef(new Animated.Value(1)).current;

  const dhikrPresets: DhikrPreset[] = [
    {
      id: '1',
      title: 'Subhan Allah',
      arabicText: 'سُبْحَانَ ٱللَّٰهِ',
      transliteration: 'Subhan Allah',
      translation: 'Glory be to Allah',
      recommendedCount: 33,
      color: colors.primary,
    },
    {
      id: '2',
      title: 'Alhamdulillah',
      arabicText: 'ٱلْحَمْدُ لِلَّٰهِ',
      transliteration: 'Alhamdulillah',
      translation: 'All praise is due to Allah',
      recommendedCount: 33,
      color: colors.secondary,
    },
    {
      id: '3',
      title: 'Allahu Akbar',
      arabicText: 'ٱللَّٰهُ أَكْبَرُ',
      transliteration: 'Allahu Akbar',
      translation: 'Allah is the Greatest',
      recommendedCount: 33,
      color: colors.accent,
    },
    {
      id: '4',
      title: 'La ilaha illallah',
      arabicText: 'لَا إِلَٰهَ إِلَّا ٱللَّٰهُ',
      transliteration: 'La ilaha illallah',
      translation: 'There is no god but Allah',
      recommendedCount: 100,
      color: colors.error,
    },
  ];

  const activePresetData = dhikrPresets.find(
    (preset) => preset.id === activePreset
  );

  // Reset counter when changing presets
  useEffect(() => {
    if (activePreset) {
      const preset = dhikrPresets.find((p) => p.id === activePreset);
      if (preset) {
        setCount(0);
        setTargetCount(preset.recommendedCount);
      }
    }
  }, [activePreset]);

  // Handle count increment with animation and haptic feedback
  const incrementCount = () => {
    if (count < targetCount) {
      // Animate the button press
      Animated.sequence([
        Animated.timing(scaleAnim, {
          toValue: 0.95,
          duration: 100,
          useNativeDriver: true,
        }),
        Animated.timing(scaleAnim, {
          toValue: 1,
          duration: 100,
          useNativeDriver: true,
        }),
      ]).start();

      // Provide haptic feedback
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);

      // Update count
      setCount((prevCount) => prevCount + 1);
    } else {
      // Session completed
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
      setCompletedSessions((prev) => prev + 1);

      // Auto-reset after a delay
      setIsResetting(true);
      setTimeout(() => {
        setCount(0);
        setIsResetting(false);
      }, 1500);
    }
  };

  // Reset counter manually
  const resetCounter = () => {
    Haptics.notificationAsync(Haptics.NotificationFeedbackType.Warning);
    setCount(0);
  };

  // Calculate progress percentage
  const progressPercentage = (count / targetCount) * 100;

  return (
    <>
      <Stack.Screen options={{ title: 'Dhikr Counter' }} />
      <ScrollView
        style={styles.container}
        contentContainerStyle={styles.contentContainer}
      >
        <View style={styles.header}>
          <Text variant="heading2">Dhikr Counter</Text>
          <Text variant="body" color="textSecondary" style={styles.subtitle}>
            Remembrance of Allah brings peace to the heart
          </Text>
        </View>

        {/* Preset Selection */}
        <View style={styles.presetsContainer}>
          <Text variant="subtitle" style={styles.sectionTitle}>
            Choose a Dhikr
          </Text>
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            style={styles.presetsScroll}
          >
            {dhikrPresets.map((preset) => (
              <TouchableOpacity
                key={preset.id}
                style={[
                  styles.presetButton,
                  activePreset === preset.id && {
                    backgroundColor: preset.color,
                    borderColor: preset.color,
                  },
                ]}
                onPress={() => setActivePreset(preset.id)}
              >
                <Text variant="subtitle" style={styles.presetButtonText}>
                  {preset.title}
                </Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>

        {/* Selected Dhikr Display */}
        {activePresetData ? (
          <Card variant="elevated" style={styles.dhikrCard}>
            <Text style={styles.arabicText}>{activePresetData.arabicText}</Text>
            <Text variant="subtitle" style={styles.transliteration}>
              {activePresetData.transliteration}
            </Text>
            <Text
              variant="body"
              color="textSecondary"
              style={styles.translation}
            >
              "{activePresetData.translation}"
            </Text>
          </Card>
        ) : (
          <Card variant="elevated" style={styles.placeholderCard}>
            <Text
              variant="subtitle"
              color="textSecondary"
              style={styles.placeholderText}
            >
              Select a dhikr from above to begin
            </Text>
          </Card>
        )}

        {/* Counter Display */}
        {activePresetData && (
          <>
            <View style={styles.counterSection}>
              <View style={styles.progressRing}>
                <RNView style={styles.progressBackground} />
                <RNView
                  style={[
                    styles.progressFill,
                    {
                      backgroundColor: activePresetData.color,
                      width: `${progressPercentage}%`,
                    },
                  ]}
                />
                <View style={styles.countDisplay}>
                  <Text variant="heading2" style={styles.countText}>
                    {count}
                  </Text>
                  <Text variant="caption" color="textSecondary">
                    of {targetCount}
                  </Text>
                </View>
              </View>

              <View style={styles.counterButtons}>
                <TouchableOpacity
                  style={styles.resetButton}
                  onPress={resetCounter}
                ></TouchableOpacity>

                <Animated.View style={[{ transform: [{ scale: scaleAnim }] }]}>
                  <TouchableOpacity
                    style={[
                      styles.countButton,
                      { backgroundColor: activePresetData.color },
                    ]}
                    onPress={incrementCount}
                    disabled={isResetting}
                  >
                    {isResetting ? (
                      <Feather name="check" size={32} color="#fff" />
                    ) : (
                      <Feather name="plus" size={32} color="#fff" />
                    )}
                  </TouchableOpacity>
                </Animated.View>

                <View style={styles.spacer} />
              </View>
            </View>

            <View style={styles.statsContainer}>
              <View style={styles.statItem}>
                <Text variant="heading3" color="primary">
                  {completedSessions}
                </Text>
                <Text variant="caption" color="textSecondary">
                  Sessions Completed
                </Text>
              </View>
              <View style={styles.statDivider} />
              <View style={styles.statItem}>
                <Text variant="heading3" color="primary">
                  {completedSessions * targetCount + count}
                </Text>
                <Text variant="caption" color="textSecondary">
                  Total Count
                </Text>
              </View>
            </View>
          </>
        )}

        <View style={styles.footer}>
          <Button
            title="Return to Home"
            variant="outline"
            size="medium"
            onPress={() => router.back()}
          />
        </View>
      </ScrollView>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  contentContainer: {
    padding: Theme.spacing.m,
    paddingBottom: Theme.spacing.xl * 2,
  },
  header: {
    marginBottom: Theme.spacing.l,
  },
  subtitle: {
    marginTop: Theme.spacing.xs,
  },
  sectionTitle: {
    marginBottom: Theme.spacing.m,
  },
  presetsContainer: {
    marginBottom: Theme.spacing.l,
  },
  presetsScroll: {
    marginLeft: -Theme.spacing.xs,
  },
  presetButton: {
    paddingHorizontal: Theme.spacing.m,
    paddingVertical: Theme.spacing.s,
    marginRight: Theme.spacing.m,
    borderRadius: Theme.borderRadius.pill,
    borderWidth: 1,
    borderColor: '#e0e0e0',
    backgroundColor: '#fff',
    ...Theme.shadows.small,
  },
  presetButtonText: {
    textAlign: 'center',
  },
  dhikrCard: {
    marginBottom: Theme.spacing.l,
    padding: Theme.spacing.m,
    alignItems: 'center',
  },
  placeholderCard: {
    marginBottom: Theme.spacing.l,
    padding: Theme.spacing.l,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 150,
  },
  placeholderIcon: {
    marginBottom: Theme.spacing.m,
  },
  placeholderText: {
    textAlign: 'center',
  },
  arabicText: {
    fontSize: 36,
    fontFamily: Theme.typography.fontFamily.arabic,
    marginBottom: Theme.spacing.m,
  },
  transliteration: {
    marginBottom: Theme.spacing.s,
    textAlign: 'center',
  },
  translation: {
    textAlign: 'center',
    fontStyle: 'italic',
  },
  counterSection: {
    alignItems: 'center',
    marginBottom: Theme.spacing.l,
  },
  progressRing: {
    width: 200,
    height: 200,
    borderRadius: 100,
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
    marginBottom: Theme.spacing.l,
    ...Theme.shadows.medium,
  },
  progressBackground: {
    position: 'absolute',
    width: '100%',
    height: '100%',
    borderRadius: 100,
    backgroundColor: '#f0f0f0',
  },
  progressFill: {
    position: 'absolute',
    height: '100%',
    borderRadius: 100,
    left: 0,
  },
  countDisplay: {
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#fff',
    width: 180,
    height: 180,
    borderRadius: 90,
    ...Theme.shadows.small,
  },
  countText: {
    fontSize: 48,
    marginBottom: Theme.spacing.xs,
  },
  counterButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    width: '100%',
    paddingHorizontal: Theme.spacing.l,
  },
  resetButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: '#f0f0f0',
    justifyContent: 'center',
    alignItems: 'center',
  },
  countButton: {
    width: 80,
    height: 80,
    borderRadius: 40,
    justifyContent: 'center',
    alignItems: 'center',
    ...Theme.shadows.medium,
  },
  spacer: {
    width: 44,
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    backgroundColor: '#fff',
    borderRadius: Theme.borderRadius.medium,
    padding: Theme.spacing.m,
    marginBottom: Theme.spacing.l,
    ...Theme.shadows.small,
  },
  statItem: {
    alignItems: 'center',
    flex: 1,
  },
  statDivider: {
    width: 1,
    height: '80%',
    backgroundColor: '#f0f0f0',
  },
  footer: {
    marginTop: Theme.spacing.l,
    alignItems: 'center',
  },
});
