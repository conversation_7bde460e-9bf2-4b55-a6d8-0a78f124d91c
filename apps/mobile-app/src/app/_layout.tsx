import React from 'react';
// Import polyfill first to fix React.use hook issue
import '../polyfills/react-use';
import { RootProvider } from '../state/context';
import FontAwesome from '@expo/vector-icons/FontAwesome';
import {
  DarkTheme,
  DefaultTheme,
  ThemeProvider,
} from '@react-navigation/native';
import { useFonts } from 'expo-font';
import { SplashScreen, Stack } from 'expo-router';
import { useEffect } from 'react';
import { StyleSheet, useColorScheme } from 'react-native';
import { GestureHandlerRootView } from 'react-native-gesture-handler';

export {
  // Catch any errors thrown by the Layout component.
  ErrorBoundary,
} from 'expo-router';

export const unstable_settings = {
  // Ensure that reloading on `/modal` keeps a back button present.
  initialRouteName: '(tabs)',
};

// Prevent the splash screen from auto-hiding before asset loading is complete.
SplashScreen.preventAutoHideAsync();

export default function RootLayout() {
  const [loaded, error] = useFonts({
    ...FontAwesome.font,
  });

  // Expo Router uses Error Boundaries to catch errors in the navigation tree.
  useEffect(() => {
    if (error) throw error;
  }, [error]);

  useEffect(() => {
    if (loaded) {
      SplashScreen.hideAsync();
    }
  }, [loaded]);

  if (!loaded) {
    return null;
  }

  return <RootLayoutNav />;
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});

function RootLayoutNav() {
  const colorScheme = useColorScheme();

  return (
    <GestureHandlerRootView style={styles.container}>
      <RootProvider>
        <ThemeProvider
          value={colorScheme === 'dark' ? DarkTheme : DefaultTheme}
        >
          <Stack>
            <Stack.Screen name="(tabs)" options={{ headerShown: false }} />
            <Stack.Screen
              name="emergency"
              options={{
                presentation: 'modal',
                title: 'Sakīna Mode',
              }}
            />
            <Stack.Screen name="journal" options={{ title: 'Journal' }} />
            <Stack.Screen
              name="knowledge"
              options={{ title: 'Knowledge Hub' }}
            />
            <Stack.Screen name="community" options={{ title: 'Community' }} />
            <Stack.Screen
              name="journeys"
              options={{ title: 'Healing Journeys' }}
            />
            <Stack.Screen
              name="practices"
              options={{ title: 'Daily Practices' }}
            />
            <Stack.Screen name="dhikr" options={{ title: 'Dhikr Counter' }} />
            <Stack.Screen
              name="symptoms"
              options={{ title: 'Symptom Assessment' }}
            />
          </Stack>
          <StatusBar style="auto" />
        </ThemeProvider>
      </RootProvider>
    </GestureHandlerRootView>
  );
}
