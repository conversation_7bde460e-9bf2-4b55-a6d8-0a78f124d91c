{"expo": {"name": "MobileApp", "slug": "@qalb-healing-workspace/mobile-app", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/icon.png", "splash": {"image": "./assets/splash.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "updates": {"fallbackToCacheTimeout": 0}, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#FFFFFF"}}, "web": {"favicon": "./assets/favicon.png", "bundler": "metro"}, "plugins": [["expo-router", {"origin": false}]], "scheme": "qalb-healing", "experiments": {"typedRoutes": true}}}