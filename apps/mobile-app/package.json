{"name": "@qalb-healing-workspace/mobile-app", "version": "0.0.1", "private": true, "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "build": "expo build", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:unit": "jest --testPathPattern=__tests__/(components|services|utils)", "test:features": "jest --testPathPattern=__tests__/features", "test:integration": "jest --testPathPattern=__tests__/integration", "test:e2e": "jest --testPathPattern=__tests__/e2e", "test:islamic": "jest --testPathPattern=islamic", "test:all": "chmod +x scripts/run-tests.sh && ./scripts/run-tests.sh", "test:ci": "jest --coverage --watchAll=false --ci", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "type-check": "tsc --noEmit", "clean": "rm -rf node_modules && rm -rf .expo && rm -rf dist", "reset": "npm run clean && npm install"}, "dependencies": {"@react-native-async-storage/async-storage": "2.1.2", "@react-navigation/native": "^7.0.14", "@react-navigation/stack": "^6.3.0", "expo": "~53.0.11", "expo-av": "~15.1.6", "expo-constants": "~17.1.6", "expo-font": "~13.3.1", "expo-linking": "~7.1.5", "expo-router": "~5.0.7", "react": "19.0.0", "react-native": "0.79.3", "react-native-gesture-handler": "~2.24.0", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-web": "^0.20.0"}, "devDependencies": {"@babel/plugin-syntax-jsx": "^7.27.1", "@babel/plugin-transform-react-jsx": "^7.27.1", "@babel/preset-env": "^7.27.2", "@babel/preset-react": "^7.27.1", "@babel/preset-typescript": "^7.27.1", "@testing-library/jest-native": "^5.4.0", "@testing-library/react-native": "^12.0.0", "@types/react": "~19.0.10", "@types/react-native": "~0.72.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "babel-plugin-module-resolver": "^5.0.2", "eslint": "^8.0.0", "eslint-config-expo": "~9.2.0", "jest": "^29.0.0", "jest-expo": "~53.0.7", "jest-html-reporters": "^3.1.0", "jest-junit": "^16.0.0", "react-test-renderer": "18.2.0", "typescript": "~5.8.3"}}