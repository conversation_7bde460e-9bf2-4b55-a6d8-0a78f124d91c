{"name": "@qalb-healing-workspace/source", "version": "0.0.0", "license": "MIT", "scripts": {"start": "nx serve", "build": "nx build", "test": "nx test", "lint": "nx lint", "typecheck": "nx typecheck", "serve:mobile-app": "nx serve mobile-app", "start:mobile-app": "nx start mobile-app", "build:mobile-app": "nx build mobile-app", "test:mobile-app": "nx test mobile-app", "lint:mobile-app": "nx lint mobile-app", "ios:mobile-app": "nx run-ios mobile-app", "android:mobile-app": "nx run-android mobile-app", "serve:backend": "nx serve backend", "build:backend": "nx build backend", "test:backend": "nx test backend", "lint:backend": "nx lint backend", "typecheck:backend": "nx typecheck backend", "start:ai-service": "cd apps/ai-service && ./run.sh", "test:ai-service": "cd apps/ai-service && python -m pytest", "build:libs": "nx build-many --target=build --projects=islamic-content,shared-types,validation", "test:libs": "nx test-many --target=test --projects=islamic-content,shared-types,validation", "lint:libs": "nx lint-many --target=lint --projects=islamic-content,shared-types,validation", "test:coverage": "nx test --coverage", "graph": "nx graph", "affected:build": "nx affected --target=build", "affected:test": "nx affected --target=test", "affected:lint": "nx affected --target=lint", "test:unit": "cd apps/mobile-app && npm test -- __tests__/simple.test.ts __tests__/utils/utils.test.ts __tests__/logic/logic.test.ts", "test:unit:verbose": "cd apps/mobile-app && npm test -- __tests__/simple.test.ts __tests__/utils/utils.test.ts __tests__/logic/logic.test.ts --verbose", "test:unit:watch": "cd apps/mobile-app && npm test -- __tests__/simple.test.ts __tests__/utils/utils.test.ts __tests__/logic/logic.test.ts --watch", "test:features": "cd apps/mobile-app && npm test -- __tests__/features/ --verbose", "test:features:onboarding": "cd apps/mobile-app && npm test -- __tests__/features/feature-0-onboarding.test.tsx --verbose", "test:features:assessment": "cd apps/mobile-app && npm test -- __tests__/features/feature-1-assessment.test.tsx --verbose", "test:features:journeys": "cd apps/mobile-app && npm test -- __tests__/features/feature-2-journeys.test.tsx --verbose", "test:features:emergency": "cd apps/mobile-app && npm test -- __tests__/features/emergency-mode.test.tsx --verbose", "test:components": "cd apps/mobile-app && npm test -- __tests__/components/ --verbose", "test:components:ui": "cd apps/mobile-app && npm test -- __tests__/components/ui.test.tsx --verbose", "test:integration": "cd apps/mobile-app && npm test -- __tests__/services/ __tests__/e2e/ --verbose", "test:integration:services": "cd apps/mobile-app && npm test -- __tests__/services/services.test.ts --verbose", "test:integration:e2e": "cd apps/mobile-app && npm test -- __tests__/e2e/end-to-end.test.tsx --verbose", "test:backend:unit": "cd apps/backend && npx jest __tests__/unit/ --config jest.config.js --verbose", "test:backend:auth": "cd apps/backend && npx jest __tests__/unit/auth.controller.test.ts --config jest.config.js --verbose", "test:backend:assessment": "cd apps/backend && npx jest __tests__/unit/assessment.controller.test.ts --config jest.config.js --verbose", "test:backend:journey": "cd apps/backend && npx jest __tests__/unit/journey.controller.test.ts --config jest.config.js --verbose", "test:backend:integration": "cd apps/backend && npx jest __tests__/integration/ --config jest.config.js --verbose", "test:ai:unit": "cd apps/ai-service && python -m pytest __tests__/unit/ -v", "test:ai:crisis": "cd apps/ai-service && python -m pytest __tests__/unit/test_crisis_detection.py -v", "test:ai:assessment": "cd apps/ai-service && python -m pytest __tests__/unit/test_assessment_ai.py -v", "test:ai:integration": "cd apps/ai-service && python -m pytest __tests__/integration/ -v", "test:islamic": "cd apps/mobile-app && npm test -- __tests__/simple.test.ts --verbose --testNamePattern='Islamic'", "test:islamic:content": "cd apps/mobile-app && npm test -- __tests__/utils/utils.test.ts --verbose --testNamePattern='Islamic'", "test:islamic:layers": "cd apps/mobile-app && npm test -- __tests__/utils/utils.test.ts --verbose --testNamePattern='Soul Layer'", "test:working": "cd apps/mobile-app && npm test -- __tests__/simple.test.ts __tests__/utils/utils.test.ts __tests__/logic/logic.test.ts --verbose", "test:all:mobile": "cd apps/mobile-app && npm test -- __tests__/ --verbose", "test:all:backend": "cd apps/backend && npx jest __tests__/ --config jest.config.js --verbose", "test:all:ai": "cd apps/ai-service && python -m pytest __tests__/ -v", "test:comprehensive": "npm run test:working && npm run test:features && npm run test:backend:unit && npm run test:ai:unit", "test:quick": "npm run test:unit", "test:full": "npm run test:all:mobile && npm run test:all:backend && npm run test:all:ai", "test:performance": "cd apps/mobile-app && npm test -- __tests__/simple.test.ts __tests__/utils/utils.test.ts __tests__/logic/logic.test.ts --verbose --detect<PERSON><PERSON>Handles", "test:debug": "cd apps/mobile-app && npm test -- __tests__/simple.test.ts --verbose --no-cache --runInBand"}, "private": true, "dependencies": {"@expo/metro-config": "~0.20.14", "@expo/metro-runtime": "~4.0.0", "@nx/expo": "21.2.0", "@nxlv/python": "^21.0.0", "@react-native-async-storage/async-storage": "1.23.1", "@react-native-community/netinfo": "^11.4.1", "@react-native-community/slider": "4.5.5", "@react-navigation/bottom-tabs": "^7.3.10", "@react-navigation/elements": "^2.4.3", "@react-navigation/native": "^7.1.6", "@supabase/supabase-js": "^2.49.9", "ajv": "^8.17.1", "axios": "^1.9.0", "cors": "^2.8.5", "date-fns": "^4.1.0", "dotenv": "^16.5.0", "expo": "~53.0.11", "expo-application": "~6.0.2", "expo-audio": "~0.3.5", "expo-av": "~15.1.6", "expo-blur": "~14.0.3", "expo-haptics": "~14.0.1", "expo-image": "~2.0.7", "expo-image-picker": "~16.0.6", "expo-linear-gradient": "~14.0.2", "expo-notifications": "~0.29.14", "expo-router": "~5.1.0", "expo-secure-store": "~14.0.1", "expo-splash-screen": "~0.29.13", "expo-status-bar": "~2.0.0", "expo-symbols": "~0.2.2", "expo-system-ui": "~4.0.9", "expo-web-browser": "~14.0.2", "express": "^4.21.2", "express-rate-limit": "^7.5.0", "express-validator": "^7.2.1", "helmet": "^8.1.0", "ioredis": "^5.6.1", "metro": "^0.82.4", "rate-limit-redis": "^4.2.1", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.3", "react-native-gesture-handler": "~2.24.0", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "^5.4.1", "react-native-svg": "~15.8.0", "react-native-svg-transformer": "~1.5.0", "react-native-web": "~0.19.13", "react-native-webview": "13.12.5", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "winston": "^3.17.0", "xss-clean": "^0.1.4", "zod": "^3.25.48"}, "devDependencies": {"@eslint/js": "^9.8.0", "@expo/cli": "~0.24.14", "@nx/eslint": "21.1.2", "@nx/eslint-plugin": "21.1.2", "@nx/express": "21.1.2", "@nx/jest": "21.1.2", "@nx/js": "21.2.0", "@nx/node": "21.1.2", "@nx/web": "21.1.2", "@nx/webpack": "21.1.2", "@pmmmwh/react-refresh-webpack-plugin": "^0.5.7", "@svgr/webpack": "^8.0.1", "@swc-node/register": "~1.9.1", "@swc/core": "~1.5.7", "@swc/helpers": "~0.5.11", "@swc/jest": "~0.2.36", "@testing-library/jest-native": "~5.4.3", "@testing-library/react-native": "~12.9.0", "@types/cors": "^2.8.18", "@types/express": "^4.17.22", "@types/express-validator": "^2.20.33", "@types/helmet": "^0.0.48", "@types/jest": "^29.5.12", "@types/node": "~18.16.9", "@types/react": "~19.0.10", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.8", "@types/winston": "^2.4.4", "babel-plugin-module-resolver": "^5.0.2", "babel-preset-expo": "~12.0.1", "eslint": "^9.8.0", "eslint-config-prettier": "^10.0.0", "jest": "^29.7.0", "jest-environment-node": "^29.7.0", "jest-expo": "~52.0.2", "jsonc-eslint-parser": "^2.1.0", "nx": "21.1.2", "prettier": "^2.6.2", "react-refresh": "^0.10.0", "react-test-renderer": "19.0.0", "ts-jest": "^29.1.0", "ts-node": "10.9.1", "tslib": "^2.3.0", "typescript": "~5.7.2", "typescript-eslint": "^8.19.0", "webpack-cli": "^5.1.4"}, "workspaces": ["packages/*", "apps/*", "libs/*", "mobile-app-v2"], "resolutions": {"react": "19.0.0", "react-dom": "19.0.0", "react-server-dom-webpack": "19.0.0", "**/react": "19.0.0", "**/react-dom": "19.0.0"}}